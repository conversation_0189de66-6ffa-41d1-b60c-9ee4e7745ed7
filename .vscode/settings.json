{
  "typescript.tsdk": "./node_modules/typescript/lib",
  "volar.tsPlugin": true,
  "volar.tsPluginStatus": false,
  "npm.packageManager": "pnpm",
  "editor.tabSize": 2,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "files.eol": "\n",
  "search.exclude": {
    "**/node_modules": true,
    "**/*.log": true,
    "**/*.log*": true,
    "**/bower_components": true,
    "**/dist": true,
    "**/elehukouben": true,
    "**/.git": true,
    "**/.gitignore": true,
    "**/.svn": true,
    "**/.DS_Store": true,
    "**/.idea": true,
    "**/.vscode": false,
    "**/yarn.lock": true,
    "**/tmp": true,
    "out": true,
    "dist": true,
    "node_modules": true,
    "CHANGELOG.md": true,
    "examples": true,
    "res": true,
    "screenshots": true,
    "yarn-error.log": true,
    "**/.yarn": true
  },
  "files.exclude": {
    "**/.cache": true,
    "**/.editorconfig": true,
    "**/.eslintcache": true,
    "**/bower_components": true,
    "**/.idea": true,
    "**/tmp": true,
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/.vscode/**": true,
    "**/node_modules/**": true,
    "**/tmp/**": true,
    "**/bower_components/**": true,
    "**/dist/**": true,
    "**/yarn.lock": true
  },
  "stylelint.enable": true,
  "stylelint.validate": ["css", "less", "postcss", "scss", "vue", "sass"],
  "path-intellisense.mappings": {
    "@/": "${workspaceRoot}/src"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit"
  },
  "[vue]": {
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit",
      "source.fixAll.stylelint": "explicit"
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "i18n-ally.localesPaths": ["src/locales/lang"],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.sortKeys": true,
  "i18n-ally.namespace": true,
  "i18n-ally.pathMatcher": "{locale}/{namespaces}.{ext}",
  "i18n-ally.enabledParsers": ["json", "ts", "js"],
  "i18n-ally.sourceLanguage": "en",
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.enabledFrameworks": ["vue", "react"],
  "cSpell.words": [
    "antd",
    "antv",
    "brotli",
    "browserslist",
    "Cascader",
    "clientid",
    "codemirror",
    "colorpicker",
    "commitlint",
    "cropperjs",
    "Datas",
    "didi",
    "echarts",
    "esnext",
    "esno",
    "exceljs",
    "Expan",
    "gitee",
    "iconify",
    "INNERLINK",
    "INTLIFY",
    "ipaddr",
    "jsencrypt",
    "Jumpto",
    "lintstagedrc",
    "logicflow",
    "logininfo",
    "logininfor",
    "mockjs",
    "nprogress",
    "oper",
    "operlog",
    "packagejson",
    "PARENTVIEW",
    "persistedstate",
    "phonenumber",
    "pinia",
    "pnpm",
    "Popconfirm",
    "preinstall",
    "qrcode",
    "ruoyi",
    "sider",
    "Snailjob",
    "sortablejs",
    "stylelint",
    "tailwindcss",
    "tinymce",
    "unocss",
    "unref",
    "vben",
    "vditor",
    "Vite",
    "vitejs",
    "vuedraggable",
    "vueuse",
    "zxcvbn"
  ],
  "vetur.format.scriptInitialIndent": true,
  "vetur.format.styleInitialIndent": true,
  "vetur.validation.script": false,
  "MicroPython.executeButton": [
    {
      "text": "▶",
      "tooltip": "运行",
      "alignment": "left",
      "command": "extension.executeFile",
      "priority": 3.5
    }
  ],
  "MicroPython.syncButton": [
    {
      "text": "$(sync)",
      "tooltip": "同步",
      "alignment": "left",
      "command": "extension.execute",
      "priority": 4
    }
  ],
  // 控制相关文件嵌套展示
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "$(capture).test.ts, $(capture).test.tsx",
    "*.tsx": "$(capture).test.ts, $(capture).test.tsx",
    "*.env": "$(capture).env.*",
    "CHANGELOG.md": "CHANGELOG*",
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,.gitattributes,.gitignore,.gitpod.yml,CNAME,README*,.npmrc,.browserslistrc",
    ".eslintrc.cjs": ".eslintignore,.prettierignore,.stylelintignore,.commitlintrc.*,.prettierrc.*,.stylelintrc.*"
  },
  "terminal.integrated.scrollback": 10000,
  "nuxt.isNuxtApp": false,
  "vscodeCustomCodeColor.highlightValue": "v-auth",
  "vscodeCustomCodeColor.highlightValueColor": "#6366f1",
  "editor.guides.bracketPairs": "active", // 括号对齐线
  "editor.cursorSmoothCaretAnimation": "on", // 光标闪烁动画
  "editor.cursorBlinking": "expand" // 光标移动动画
}
