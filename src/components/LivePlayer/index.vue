<template>
  <iframe
    :key="props.path"
    ref="iframeRef"
    :src="iframeSrc"
    frameborder="0"
    allowfullscreen
    loading="lazy"
    :style="{
      width: '100%',
      height: '100%',
    }"
  ></iframe>
</template>

<script setup>
  import { ref, watchEffect } from 'vue';

  const props = defineProps({
    path: {
      type: String,
      required: true,
      default: '',
    },
    headers: {
      type: Object,
      default: () => ({
        authorization: 'Basic YWRtaW46YWRtaW4xMjM=',
      }),
    },
  });

  const iframeRef = ref(null);

  const baseUrl = window.location.origin + import.meta.env.VITE_PUBLIC_PATH;

  const iframeSrc = `${baseUrl}/media/index.html`;

  watchEffect(() => {
    if (!iframeRef.value) {
      return false;
    }

    iframeRef.value.onload = () => {
      iframeRef.value.contentWindow.postMessage(
        {
          type: 'injectParams',
          path: props.path,
          headers: props.headers,
        },
        '*',
      );
    };
  });
</script>
