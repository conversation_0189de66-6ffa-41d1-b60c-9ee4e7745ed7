import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery } from '@/api/base';
import { ItemRecord } from './model';

enum Api {
  root = '/business/itemRecord',
  itemRecordList = '/business/itemRecord/list',
}

export function itemRecordList(params?: PageQuery) {
  return securityHttp.get<ItemRecord[]>({ url: Api.itemRecordList, params });
}

export function itemRecordInfo(itemRecordId: ID) {
  return securityHttp.get<ItemRecord>({ url: Api.root + '/' + itemRecordId });
}

export function itemRecordAdd(data: ItemRecord) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function itemRecordUpdate(data: ItemRecord) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function itemRecordRemove(itemRecordIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + itemRecordIds });
}
