import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { WorkerInfo } from './model';

enum Api {
  root = '/business/workerInfo',
  workerInfoSelect = '/business/workerInfo/optionSelect',
  workerInfoList = '/business/workerInfo/list',
  workerInfoExport = '/business/workerInfo/export',
}

export function workerInfoList(params?: PageQuery) {
  return securityHttp.get<WorkerInfo>({ url: Api.workerInfoList, params });
}

export function workerInfoDetail(workerInfoId: ID) {
  return securityHttp.get<WorkerInfo>({ url: Api.root + '/' + workerInfoId });
}

export function workerInfoExport(data: any) {
  return securityCommonExport(Api.workerInfoExport, data);
}

export function workerInfoAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function workerInfoUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function workerInfoRemove(workerInfoIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + workerInfoIds });
}

export function workerInfoOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.workerInfoSelect, params });
}
