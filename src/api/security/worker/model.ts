export interface WorkerInfo {
  createBy?: number; // 创建者
  createDept?: number; // 创建部门
  createTime?: Date; // 创建时间
  gender: string; // 性别
  id: number; // 主键id
  image: string; // 人员人脸图像（用于人脸识别）
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  phone: string; // 联系方式
  qualificationInformation?: string; // 资质信息(勾选字典表)
  remark?: string; // 备注
  stationId: string; // 所属站点id
  stationName: string; // 所属站点名称
  type: string; // 人员类型：1:自有人员、2:合作人员
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  userName: string; // 姓名
  violationCount?: number; // 累计违规次数
  workerNum: string; // 工号
}
