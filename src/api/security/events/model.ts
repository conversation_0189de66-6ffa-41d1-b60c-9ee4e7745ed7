import { BaseEntity } from '@/api/base';

/**
 * 事件管理信息对象
 */
export interface EventManagementInfo extends BaseEntity {
  id?: number; // 事件编号(Id唯一标识)
  eventType: string; // 事件类型：1:大件物品违规拿取异常、出库与工单不一致异常、入库与工单不一致异常、2:车辆监测点异常、检测项遗漏异常、3:作业人员不一致异常、上下道不一致异常、接地线异常、安全帽异常、工装异常、绝缘鞋异常、绝缘手套异常
  eventLevel: string; // 事件级别：1:紧急、2:重大、3:一般
  status: string; // 事件状态：1:未处理、2:已处理
  occurrenceTime: string; // 发生时间
  responsiblePerson?: string; // 责任人
  taskId?: string; // 关联任务id
  taskType: string; // 任务类型：1:料库任务、2:车辆检修任务、3作业管控任务
  arGlassId?: string; // 使用ar眼镜id
  arGlassNum?: string; // 使用ar眼镜编号
  annexUrl?: string; // 附件地址
  annexType?: string; // 附件类型
  remark?: string; // 备注
}

/**
 * 事件管理业务对象
 */
export interface EventManagementInfoBo extends EventManagementInfo {
  params?: { [key: string]: any }; // 请求参数
}

/**
 * 事件管理视图对象
 */
export interface EventManagementInfoVo extends EventManagementInfo {
  // 可以添加额外的视图字段
}

/**
 * 表格分页数据对象
 */
export interface TableDataInfoEventManagementInfoVo {
  total: number; // 总记录数
  rows: EventManagementInfoVo[]; // 列表数据
  code: number; // 消息状态码
  msg: string; // 消息内容
}

/**
 * 今日事件概览统计
 */
export interface TodayEventOverview {
  todayTotalCount: number; // 今日异常事件总数
  vehicleCount: number; // 车辆检修异常事件总数
  materialCount: number; // 物料仓库异常事件总数
  controlCount: number; // 作业管控异常事件总数
}
