import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { 
  EventManagementInfo, 
  EventManagementInfoBo, 
  EventManagementInfoVo, 
  TableDataInfoEventManagementInfoVo,
  TodayEventOverview 
} from './model';

enum Api {
  root = '/business/managementInfo',
  list = '/business/managementInfo/list',
  export = '/business/managementInfo/export',
  getToDayEventOverview = '/business/managementInfo/getToDayEventOverview',
}

/**
 * 查询事件管理列表
 */
export function eventManagementList(params?: PageQuery) {
  return securityHttp.get<TableDataInfoEventManagementInfoVo>({ url: Api.list, params });
}

/**
 * 获取事件管理详细信息
 */
export function eventManagementInfo(id: ID) {
  return securityHttp.get<{ data: EventManagementInfoVo }>({ url: Api.root + '/' + id });
}

/**
 * 新增事件管理
 */
export function eventManagementAdd(data: EventManagementInfoBo) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

/**
 * 修改事件管理
 */
export function eventManagementUpdate(data: EventManagementInfoBo) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

/**
 * 删除事件管理
 */
export function eventManagementRemove(ids: IDS) {
  return securityHttp.deleteWithMsg<void>({ url: Api.root + '/' + ids });
}

/**
 * 导出事件管理列表
 */
export function eventManagementExport(data: any) {
  return securityCommonExport(Api.export, data);
}

/**
 * 获取今日事件概览统计
 */
export function getToDayEventOverview(params?: any) {
  return securityHttp.get<TodayEventOverview>({ url: Api.getToDayEventOverview, params });
}
