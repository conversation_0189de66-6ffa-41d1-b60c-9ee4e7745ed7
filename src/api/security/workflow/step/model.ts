export interface WorkflowStep {
  id: number; // 环节 ID
  stepName: string; // 环节名称
  roleType: string; // 角色类型，枚举值：leader, contact, guardian
  stepOrder: number; // 环节顺序
  description?: string; // 环节描述
  annexType?: string; // 附件类型：1:图片、2:语音、3:视频、99:无附件
  annexDescription?: string; // 附件要求描述
  isMultipleAttachments?: string; // 是否允许附件多个：1:是、2:否
  preStepIds?: string; // 前置环节 ID（JSON 数组）
  isKeyPoint: number; // 是否重点检修点（1 = 是，0 = 否）
  remark?: string; // 备注
  createDept?: number; // 创建部门
  createBy?: number; // 创建者
  createTime?: string; // 创建时间
  updateBy?: number; // 更新者
  updateTime?: string; // 更新时间
  params?: object; // 请求参数
  extra?: object; // 额外字段
}

export interface PageQuery {
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;
}

export type ID = number;
export type IDS = number[];
