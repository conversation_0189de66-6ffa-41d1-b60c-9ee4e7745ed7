import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery } from './model';

enum Api {
  root = '/business/workflowStep',
  workflowStepList = '/business/workflowStep/list',
  workflowStepExport = '/business/workflowStep/export',
}

export function workflowStepList(params?: PageQuery) {
  return securityHttp.get<WorkflowStep[]>({ url: Api.workflowStepList, params });
}

export function workflowStepInfo(workflowStepId: ID) {
  return securityHttp.get<WorkflowStep>({ url: Api.root + '/' + workflowStepId });
}

export function workflowStepExport(data: any) {
  return securityHttp.post({ url: Api.workflowStepExport, data });
}

export function workflowStepAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function workflowStepUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function workflowStepRemove(workflowStepIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + workflowStepIds });
}

export function workflowStepOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.root + '/optionSelect', params });
}
