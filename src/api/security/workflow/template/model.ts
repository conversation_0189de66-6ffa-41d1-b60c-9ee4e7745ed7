export interface WorkflowTemplate {
  id: number; // 流程模板 ID
  deviceNo: string; // 设备标识id
  model: string; // 型号
  factoryNo: string; // 出厂编号
  stationId: string; // 站点id
  stationName: string; // 站点名称
  name: string; // 流程模板名称
  description?: string; // 流程模板描述
  status: string; // 状态，枚举值：active, inactive, deleted
  workflowCount: number; // 该模板有多少环节数量
  remark?: string; // 备注
  createDept?: number; // 创建部门
  createBy?: number; // 创建者
  createTime?: string; // 创建时间
  updateBy?: number; // 更新者
  updateTime?: string; // 更新时间
  params?: object; // 请求参数
}
