import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { RecordedBroadcast } from './model';

enum Api {
  root = '/business/recordedBroadcast',
  recordedBroadcastList = '/business/recordedBroadcast/list',
  recordedBroadcastExport = '/business/recordedBroadcast/export',
}

export function recordedBroadcastList(params?: PageQuery) {
  return securityHttp.get<RecordedBroadcast>({ url: Api.recordedBroadcastList, params });
}

export function recordedBroadcastInfo(recordedBroadcastId: ID) {
  return securityHttp.get<RecordedBroadcast>({ url: Api.root + '/' + recordedBroadcastId });
}

export function recordedBroadcastExport(data: any) {
  return securityCommonExport(Api.recordedBroadcastExport, data);
}

export function recordedBroadcastAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function recordedBroadcastUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function recordedBroadcastRemove(recordedBroadcastIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + recordedBroadcastIds });
}
