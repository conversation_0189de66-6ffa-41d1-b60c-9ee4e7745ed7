import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { InspectionItem } from './model';

enum Api {
  root = '/business/inspectionItem',
  inspectionItemSelect = '/business/inspectionItem/optionSelect',
  inspectionItemList = '/business/inspectionItem/list',
  inspectionItemExport = '/business/inspectionItem/export',
}

export function inspectionItemList(params?: PageQuery) {
  return securityHttp.get<InspectionItem>({ url: Api.inspectionItemList, params });
}

export function inspectionItemDetail(inspectionItemId: ID) {
  return securityHttp.get<InspectionItem>({ url: Api.root + '/' + inspectionItemId });
}

export function inspectionItemExport(data: any) {
  return securityCommonExport(Api.inspectionItemExport, data);
}

export function inspectionItemAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function inspectionItemUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function inspectionItemRemove(inspectionItemIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + inspectionItemIds });
}

export function inspectionItemOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.inspectionItemSelect, params });
}
