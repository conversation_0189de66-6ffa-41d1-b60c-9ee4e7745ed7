import { securityHttp } from '@/utils/http/axios';
import { TemplateStep } from './model';
import type { WorkflowStep } from '@/api/security/railwayWorkflowStep/model';

enum Api {
  allocatedStepListByTemplateId = '/business/workflowTemplate/allocatedStepListByTemplateId',
  unallocatedStepListByTemplateId = '/business/workflowTemplate/unallocatedStepListByTemplateId',
  templateStep = '/business/templateStep',
}

export function allocatedStepListByTemplateId(params: WorkflowStep) {
  return securityHttp.get<WorkflowStep[]>({
    url: Api.allocatedStepListByTemplateId,
    params,
  });
}

export function unallocatedStepListByTemplateId(params: WorkflowStep) {
  return securityHttp.get<WorkflowStep[]>({
    url: Api.unallocatedStepListByTemplateId,
    params,
  });
}

export function assignTemplateStep(data: TemplateStep) {
  return securityHttp.postWithMsg<void>({ url: Api.templateStep, data });
}

export function unassignTemplateStep(workflowTemplateIds: number[]) {
  return securityHttp.deleteWithMsg({ url: Api.templateStep + '/' + workflowTemplateIds });
}
