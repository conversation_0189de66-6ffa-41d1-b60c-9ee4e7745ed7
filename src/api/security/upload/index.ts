import { UploadApiResult } from './model';
import { securityHttp } from '@/utils/http/axios';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';

/**
 * @description: Upload interface
 */
export function securityUploadApi(
  params: UploadFileParams,
  onUploadProgress?: (progressEvent: AxiosProgressEvent) => void,
) {
  return securityHttp.uploadFile<UploadApiResult>(
    {
      url: '/resource/oss/upload',
      onUploadProgress,
    },
    params,
  );
}
