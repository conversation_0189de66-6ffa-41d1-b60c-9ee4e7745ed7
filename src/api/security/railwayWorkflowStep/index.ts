import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { WorkflowStep } from './model';

enum Api {
  root = '/business/workflowStep',
  workflowStepSelect = '/business/workflowStep/optionSelect',
  workflowStepList = '/business/workflowStep/list',
  workflowStepExport = '/business/workflowStep/export',
  getPreStageStepList = '/business/workflowStep/getPreStageStepListById',
}

export function workflowStepList(params?: PageQuery) {
  return securityHttp.get<WorkflowStep>({ url: Api.workflowStepList, params });
}

export function workflowStepInfo(workflowStepId: ID) {
  return securityHttp.get<WorkflowStep>({ url: Api.root + '/' + workflowStepId });
}

export function workflowStepExport(data: any) {
  return securityCommonExport(Api.workflowStepExport, data);
}

export function workflowStepAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function workflowStepUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function workflowStepRemove(workflowStepIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + workflowStepIds });
}

export function workflowStepOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.workflowStepSelect, params });
}

export function workflowStepPreStageStepListById(params?: any) {
  return securityHttp.get({ url: Api.getPreStageStepList, params });
}
