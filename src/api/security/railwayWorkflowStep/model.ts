export interface WorkflowStep {
  annexDescription?: string; // 附件要求描述
  annexType?: string; // 附件类型：1:图片、2:语音、3:视频、99:无附件
  createBy?: number; // 创建者
  createDept?: number; // 创建部门
  createTime?: Date; // 创建时间
  description?: string; // 环节描述
  id: number; // 环节 ID
  isKeyPoint: number; // 是否重点检修点（1 = 是，0 = 否）
  isMultipleAttachments?: string; // 是否允许附件多个：1:是、2:否
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  preStepIds?: string; // 前置环节 ID（JSON 数组）
  remark?: string; // 备注
  roleType: string; // 角色类型，枚举值：leader, contact, guardian
  stepName: string; // 环节名称
  stepOrder: number; // 环节顺序
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
}
