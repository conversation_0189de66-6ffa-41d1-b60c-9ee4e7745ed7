import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { InspectionPoint } from './model';

enum Api {
  root = '/business/inspectionPoint',
  inspectionPointList = '/business/inspectionPoint/list',
  inspectionPointExport = '/business/inspectionPoint/export',
}

export function inspectionPointList(params?: PageQuery) {
  return securityHttp.get<InspectionPoint>({ url: Api.inspectionPointList, params });
}

export function inspectionPointInfo(inspectionPointId: ID) {
  return securityHttp.get<InspectionPoint>({ url: Api.root + '/' + inspectionPointId });
}

export function inspectionPointExport(data: any) {
  return securityCommonExport(Api.inspectionPointExport, data);
}

export function inspectionPointAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function inspectionPointUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function inspectionPointRemove(inspectionPointIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + inspectionPointIds });
}
