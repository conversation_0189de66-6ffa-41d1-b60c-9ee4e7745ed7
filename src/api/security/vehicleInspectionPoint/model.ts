export interface InspectionPoint {
  checkStandard: string; // 检查标准及方法
  createBy?: number; // 创建者
  createDept?: number; // 创建部门
  createTime?: Date; // 创建时间
  id: number; // 主键id
  isAbnormalCheckpoint: number; // 是否异常检查点（1 = 是，0 = 否）
  isAsc?: string; // 排序的方向desc或者asc
  isKeyPoint: number; // 是否重点检修点（1 = 是，0 = 否）
  itemId: string; // 所属检修项编号（外键）
  locationDesc: string; // 位置描述
  orderByColumn?: string; // 排序列
  orderNum: number; // 顺序
  pageNum?: number; // 当前页数
  pageSize?: number; // 分页大小
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  pointName: string; // 检修点名称
  pointNum: string; // 检修点编号
  remark?: string; // 备注
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
}
}
