import { securityHttp } from '@/utils/http/axios';
import { LoginParams, LoginResult, UserInfoResult } from './model';
import { ErrorMessageMode } from '#/axios';
import { useGlobSetting } from '@/hooks/setting';

enum Api {
  Login = '/api/login',
  Logout = '/api/logout',
  GetUserInfo = '/system/user/getInfo',
}

const globSetting = useGlobSetting();
/**
 * @description 登录接口 可处理普通登录和oauth登录
 */
export function securityLoginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return securityHttp.get<LoginResult>(
    {
      url: Api.Login,
      params: {
        ...params,
        clientId: globSetting.clientId,
      },
    },
    {
      errorMessageMode: mode,
      encrypt: false,
    },
  );
}

/**
 * @description: 获取用户信息
 */
export function securityGetUserInfo() {
  return securityHttp.get<UserInfoResult>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

/**
 * 用户登出
 * @returns
 */
export function securityDoLogout() {
  return securityHttp.post<void>({ url: Api.Logout });
}
