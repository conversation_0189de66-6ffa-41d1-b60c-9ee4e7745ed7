export interface InspectionTask {
  abnormalCount: number; // 异常点总数
  completedItems: number; // 已完成项数
  createBy?: number; // 创建者
  createDept?: number; // 创建部门
  createTime?: Date; // 创建时间
  endTime?: Date; // 结束时间
  id: number; // 主键
  licensePlate: string; // 车牌号
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  remark?: string; // 备注
  startTime?: Date; // 开始时间
  stationId: string; // 所属站点id
  stationName: string; // 所属站点名称
  status: string; // 状态（进行中 / 已完成）
  totalItems: number; // 总检修项数
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  userId: string; // 执行人
  vehicleModel: string; // 车辆型号
  workTicketNo: string; // 工作票号
}

// 检修报告相关类型定义
export interface VehicleTaskItemReportVo {
  taskId: number;
  number: number;
  itemNum: string;
  itemName: string;
  createTime: string;
  totalPoints: number;
  unPassPoints: number;
  passPoints: number;
  grabberUrl: string;
  checkResult: number; // 0=合格, 1=不合格, 2=待确认
  checkRemark: string;
  userId: string;
  userName: string;
}

export interface VehicleTaskReportVo {
  taskId: number;
  totalItems: number; // 总检修项数
  completedItems: number; // 已完成项数
  abnormalCount: number; // 异常数量
  totalPoints: number; // 总检查点数
  passPoints: number; // 合格点数
  unPassPoints: number; // 不合格点数
  doPassPoints: number; // 待确认点数
  completionRate: string; // 完成率
  vehicleTaskItemReportVos: VehicleTaskItemReportVo[]; // 检修项结果
}

export interface VehicleTaskReportResponse {
  code: number;
  msg: string;
  token: string;
  uri: string;
  data: VehicleTaskReportVo;
  request_time: string;
  response_time: string;
  cost_time: string;
  debug_image_url: string;
}
