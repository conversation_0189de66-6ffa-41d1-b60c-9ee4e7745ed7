import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { InspectionTask, VehicleTaskReportVo } from './model';

enum Api {
  root = '/business/inspectionTask',
  inspectionTaskList = '/business/inspectionTask/list',
  inspectionTaskCount = '/business/inspectionTask/count',
  inspectionTaskExport = '/business/inspectionTask/export',
}

export function inspectionTaskList(params?: PageQuery) {
  return securityHttp.get<InspectionTask>({ url: Api.inspectionTaskList, params });
}

export function inspectionTaskCount(params?: any) {
  return securityHttp.get<{ status: number; num: number }[]>({
    url: Api.inspectionTaskCount,
    params,
  });
}

export function inspectionTaskInfo(inspectionTaskId: ID) {
  return securityHttp.get<InspectionTask>({ url: Api.root + '/' + inspectionTaskId });
}

export function inspectionTaskExport(data: any) {
  return securityCommonExport(Api.inspectionTaskExport, data);
}

export function inspectionTaskAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function inspectionTaskUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function inspectionTaskRemove(inspectionTaskIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + inspectionTaskIds });
}

export function inspectionTaskOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.root + '/optionSelect', params });
}

export function inspectionTaskReport(taskId: ID) {
  return securityHttp.post<VehicleTaskReportVo>({ url: Api.root + '/report', data: { taskId } });
}
