import { BaseEntity, PageQuery } from '@/api/base';

/**
 * 料库任务管理业务对象
 */
export interface MaterialWarehouseTaskBo extends BaseEntity {
  /** Id唯一标识 */
  id?: number;
  /** 任务编号(LKRW-YYYYMMDDHHmm)年月日时分秒 */
  taskNum?: string;
  /** 施工日计划编号 */
  constructionDayPlanNum?: string;
  /** 站点id */
  stationId?: string;
  /** 站点名称 */
  stationName?: string;
  /** 任务状态：1:待领取，2:待归还，3:已结束 */
  status?: string;
  /** 执行人ID */
  userId?: string;
  /** 执行人名称 */
  userName?: string;
  /** 工具种类 */
  utensilTypeCount?: number;
  /** 工具数量 */
  utensilCount?: number;
  /** 备注 */
  remark?: string;
  /** 签发时间 */
  issueTime?: string;
  /** 审核工作票命令号 */
  reviewWorkTicketCommandNum?: string;
  /** 准许作业命令号 */
  permittedOperationCommandNum?: string;
  /** 单位 */
  company?: string;
  /** 工作票签发人 */
  workTicketIssuer?: string;
  /** 是审核人 */
  reviewer?: string;
  /** 工作领导人 */
  workLeader?: string;
  /** 驻站联络人 */
  stationLiaisonPerson?: string;
  /** 地线监护人 */
  groundLineGuardian?: string;
  /** 工作组员 */
  workTeam?: string;
  /** 停电范围 */
  powerOutageRange?: string;
  /** 作业范围 */
  scopeWork?: string;
  /** 工作任务 */
  workTask?: string;
  /** 计划工作时间 */
  plannedWorkingHours?: string;
  /** 任务工具列表 */
  taskUtensils?: MaterialWarehouseTaskUtensilListBo[];
}

/**
 * 料库任务管理视图对象
 */
export interface MaterialWarehouseTaskVo {
  /** Id唯一标识 */
  id?: number;
  /** 任务编号(LKRW-YYYYMMDDHHmm)年月日时分秒 */
  taskNum?: string;
  /** 施工日计划编号 */
  constructionDayPlanNum?: string;
  /** 站点id */
  stationId?: string;
  /** 站点名称 */
  stationName?: string;
  /** 任务状态：1:待领取，2:待归还，3:已结束 */
  status?: string;
  /** 执行人ID */
  userId?: string;
  /** 执行人名称 */
  userName?: string;
  /** 工具种类 */
  utensilTypeCount?: number;
  /** 工具数量 */
  utensilCount?: number;
  /** 备注 */
  remark?: string;
  /** 签发时间 */
  issueTime?: string;
  /** 审核工作票命令号 */
  reviewWorkTicketCommandNum?: string;
  /** 准许作业命令号 */
  permittedOperationCommandNum?: string;
  /** 单位 */
  company?: string;
  /** 工作票签发人 */
  workTicketIssuer?: string;
  /** 是审核人 */
  reviewer?: string;
  /** 工作领导人 */
  workLeader?: string;
  /** 驻站联络人 */
  stationLiaisonPerson?: string;
  /** 地线监护人 */
  groundLineGuardian?: string;
  /** 工作组员 */
  workTeam?: string;
  /** 停电范围 */
  powerOutageRange?: string;
  /** 作业范围 */
  scopeWork?: string;
  /** 工作任务 */
  workTask?: string;
  /** 计划工作时间 */
  plannedWorkingHours?: string;
}

/**
 * 料库任务工具清单业务对象
 */
export interface MaterialWarehouseTaskUtensilListBo extends BaseEntity {
  /** 任务编号(Id唯一标识) */
  id?: number;
  /** 料库任务id */
  taskId?: string;
  /** 工具id */
  utensilId?: string;
  /** 工具名称 */
  utensilName?: string;
  /** 工具型号 */
  utensilType?: string;
  /** 工具数量 */
  utensilCount?: number;
  /** 领取数量 */
  receiveCount?: number;
  /** 归还数量 */
  returnCount?: number;
  /** 任务状态：1:待领取，2:待归还，3:已结束 */
  status?: string;
  /** 领取时间 */
  receiveTime?: string;
  /** 归还时间 */
  returnTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 料库任务工具清单视图对象
 */
export interface MaterialWarehouseTaskUtensilListVo {
  /** 任务编号(Id唯一标识) */
  id?: number;
  /** 料库任务id */
  taskId?: string;
  /** 工具id */
  utensilId?: string;
  /** 工具名称 */
  utensilName?: string;
  /** 工具型号 */
  utensilType?: string;
  /** 工具数量 */
  utensilCount?: number;
  /** 领取数量 */
  receiveCount?: number;
  /** 归还数量 */
  returnCount?: number;
  /** 任务状态：1:待领取，2:待归还，3:已结束 */
  status?: string;
  /** 领取时间 */
  receiveTime?: string;
  /** 归还时间 */
  returnTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 料库任务查询参数
 */
export interface MaterialWarehouseTaskQuery extends PageQuery {
  /** Id唯一标识 */
  id?: number;
  /** 任务编号(LKRW-YYYYMMDDHHmm)年月日时分秒 */
  taskNum?: string;
  /** 施工日计划编号 */
  constructionDayPlanNum?: string;
  /** 站点id */
  stationId?: string;
  /** 站点名称 */
  stationName?: string;
  /** 任务状态：1:待领取，2:待归还，3:已结束 */
  status?: string;
  /** 执行人ID */
  userId?: string;
  /** 执行人名称 */
  userName?: string;
  /** 工具种类 */
  utensilTypeCount?: number;
  /** 工具数量 */
  utensilCount?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 料库任务工具清单查询参数
 */
export interface MaterialWarehouseTaskUtensilListQuery extends PageQuery {
  /** 任务编号(Id唯一标识) */
  id?: number;
  /** 料库任务id */
  taskId?: string;
  /** 工具id */
  utensilId?: string;
  /** 工具名称 */
  utensilName?: string;
  /** 工具型号 */
  utensilType?: string;
  /** 工具数量 */
  utensilCount?: string;
  /** 任务状态：1:待领取，2:待归还，3:已结束 */
  status?: string;
  /** 领取时间 */
  receiveTime?: string;
  /** 归还时间 */
  returnTime?: string;
  /** 备注 */
  remark?: string;
}

/**
 * 表格分页数据对象 - 料库任务
 */
export interface TableDataInfoMaterialWarehouseTaskVo {
  /** 总记录数 */
  total?: number;
  /** 列表数据 */
  rows?: MaterialWarehouseTaskVo[];
  /** 消息状态码 */
  code?: number;
  /** 消息内容 */
  msg?: string;
}

/**
 * 表格分页数据对象 - 料库任务工具清单
 */
export interface TableDataInfoMaterialWarehouseTaskUtensilListVo {
  /** 总记录数 */
  total?: number;
  /** 列表数据 */
  rows?: MaterialWarehouseTaskUtensilListVo[];
  /** 消息状态码 */
  code?: number;
  /** 消息内容 */
  msg?: string;
}

/**
 * 响应信息主体 - 料库任务
 */
export interface RMaterialWarehouseTaskVo {
  /** 状态码 */
  code?: number;
  /** 消息 */
  msg?: string;
  /** token */
  token?: string;
  /** uri */
  uri?: string;
  /** 数据 */
  data?: MaterialWarehouseTaskVo;
}

/**
 * 响应信息主体 - 料库任务工具清单
 */
export interface RMaterialWarehouseTaskUtensilListVo {
  /** 状态码 */
  code?: number;
  /** 消息 */
  msg?: string;
  /** 数据 */
  data?: MaterialWarehouseTaskUtensilListVo;
}
