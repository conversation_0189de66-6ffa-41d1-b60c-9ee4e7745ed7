import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, securityCommonExport } from '@/api/base';
import {
  MaterialWarehouseTaskBo,
  MaterialWarehouseTaskQuery,
  MaterialWarehouseTaskUtensilListBo,
  MaterialWarehouseTaskUtensilListQuery,
  TableDataInfoMaterialWarehouseTaskVo,
  TableDataInfoMaterialWarehouseTaskUtensilListVo,
  RMaterialWarehouseTaskVo,
  RMaterialWarehouseTaskUtensilListVo,
} from './model';

enum Api {
  // 料库任务管理
  warehouseTask = '/business/warehouseTask',
  warehouseTaskList = '/business/warehouseTask/list',
  warehouseTaskCount = '/business/warehouseTask/count',
  warehouseTaskExport = '/business/warehouseTask/export',

  // 料库任务工具清单
  warehouseTaskUtensilList = '/business/warehouseTaskUtensilList',
  warehouseTaskUtensilListList = '/business/warehouseTaskUtensilList/list',
  warehouseTaskUtensilListExport = '/business/warehouseTaskUtensilList/export',
}

// ==================== 料库任务管理 ====================

/**
 * 查询料库任务管理列表
 */
export function warehouseTaskList(params?: MaterialWarehouseTaskQuery) {
  return securityHttp.get<TableDataInfoMaterialWarehouseTaskVo>({
    url: Api.warehouseTaskList,
    params,
  });
}

/**
 * 查询料库任务统计数据
 */
export function warehouseTaskCount(params?: any) {
  return securityHttp.get<{ status: number; num: number }[]>({
    url: Api.warehouseTaskCount,
    params,
  });
}

/**
 * 获取料库任务管理详细信息
 */
export function warehouseTaskInfo(taskId: ID) {
  return securityHttp.get<RMaterialWarehouseTaskVo>({
    url: Api.warehouseTask + '/' + taskId,
  });
}

/**
 * 新增料库任务管理
 */
export function warehouseTaskAdd(data: MaterialWarehouseTaskBo) {
  return securityHttp.post({
    url: Api.warehouseTask,
    data,
  });
}

/**
 * 修改料库任务管理
 */
export function warehouseTaskUpdate(data: MaterialWarehouseTaskBo) {
  return securityHttp.put({
    url: Api.warehouseTask,
    data,
  });
}

/**
 * 删除料库任务管理
 */
export function warehouseTaskRemove(taskIds: IDS) {
  return securityHttp.delete({
    url: Api.warehouseTask + '/' + taskIds.join(','),
  });
}

/**
 * 导出料库任务管理列表
 */
export function warehouseTaskExport(data: MaterialWarehouseTaskQuery) {
  return securityCommonExport(Api.warehouseTaskExport, data);
}

// ==================== 料库任务工具清单 ====================

/**
 * 查询料库任务工具清单列表
 */
export function warehouseTaskUtensilListList(params?: MaterialWarehouseTaskUtensilListQuery) {
  return securityHttp.get<TableDataInfoMaterialWarehouseTaskUtensilListVo>({
    url: Api.warehouseTaskUtensilListList,
    params,
  });
}

/**
 * 获取料库任务工具清单详细信息
 */
export function warehouseTaskUtensilListInfo(utensilListId: ID) {
  return securityHttp.get<RMaterialWarehouseTaskUtensilListVo>({
    url: Api.warehouseTaskUtensilList + '/' + utensilListId,
  });
}

/**
 * 新增料库任务工具清单
 */
export function warehouseTaskUtensilListAdd(data: MaterialWarehouseTaskUtensilListBo) {
  return securityHttp.post({
    url: Api.warehouseTaskUtensilList,
    data,
  });
}

/**
 * 修改料库任务工具清单
 */
export function warehouseTaskUtensilListUpdate(data: MaterialWarehouseTaskUtensilListBo) {
  return securityHttp.put({
    url: Api.warehouseTaskUtensilList,
    data,
  });
}

/**
 * 删除料库任务工具清单
 */
export function warehouseTaskUtensilListRemove(utensilListIds: IDS) {
  return securityHttp.delete({
    url: Api.warehouseTaskUtensilList + '/' + utensilListIds.join(','),
  });
}

/**
 * 导出料库任务工具清单列表
 */
export function warehouseTaskUtensilListExport(data: MaterialWarehouseTaskUtensilListQuery) {
  return securityCommonExport(Api.warehouseTaskUtensilListExport, data);
}

// ==================== 便捷方法 ====================

/**
 * 根据任务ID获取工具清单
 */
export function getUtensilListByTaskId(taskId: string) {
  return warehouseTaskUtensilListList({ taskId });
}

/**
 * 批量更新工具清单状态
 */
export function batchUpdateUtensilListStatus(utensilListIds: IDS, status: string) {
  const promises = utensilListIds.map((id) =>
    warehouseTaskUtensilListUpdate({ id: Number(id), status }),
  );
  return Promise.all(promises);
}
