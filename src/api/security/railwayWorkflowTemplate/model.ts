export interface WorkflowTemplate {
  createBy?: number; // 创建者
  createDept?: number; // 创建部门
  createTime?: Date; // 创建时间
  description?: string; // 流程模板描述
  id: number; // 流程模板 ID
  name: string; // 流程模板名称
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
  remark?: string; // 备注
  status: string; // 状态，枚举值：active, inactive, deleted
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  workflowCount: number; // 该模板有多少环节数量
}
