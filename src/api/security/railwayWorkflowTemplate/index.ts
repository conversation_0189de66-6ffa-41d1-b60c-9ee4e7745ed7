import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { WorkflowTemplate } from './model';

enum Api {
  root = '/business/workflowTemplate',
  workflowTemplateSelect = '/business/workflowTemplate/optionSelect',
  workflowTemplateList = '/business/workflowTemplate/list',
  workflowTemplateExport = '/business/workflowTemplate/export',
}

export function workflowTemplateList(params?: PageQuery) {
  return securityHttp.get<WorkflowTemplate>({ url: Api.workflowTemplateList, params });
}

export function workflowTemplateInfo(workflowTemplateId: ID) {
  return securityHttp.get<WorkflowTemplate>({ url: Api.root + '/' + workflowTemplateId });
}

export function workflowTemplateExport(data: any) {
  return securityCommonExport(Api.workflowTemplateExport, data);
}

export function workflowTemplateAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function workflowTemplateUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function workflowTemplateRemove(workflowTemplateIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + workflowTemplateIds });
}

export function workflowTemplateOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.workflowTemplateSelect, params });
}
