import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { Glasses } from './model';

enum Api {
  root = '/business/glassDevice',
  glassesSelect = '/business/glassDevice/optionSelect',
  glassesList = '/business/glassDevice/list',
  glassesExport = '/business/glassDevice/export',
  glassesListByStatus = '/business/glassDevice/listByStatus',
}

// 获取设备列表
export function glassesList(params?: PageQuery) {
  return securityHttp.get<Glasses>({ url: Api.glassesList, params });
}

// 获取单个设备信息
export function glassesInfo(glassesId: ID) {
  return securityHttp.get<Glasses>({ url: Api.root + '/' + glassesId });
}

// 导出设备信息
export function glassesExport(data: any) {
  return securityCommonExport(Api.glassesExport, data);
}

// 新增设备
export function glassesAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

// 更新设备信息
export function glassesUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

// 删除设备
export function glassesRemove(glassesIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + glassesIds });
}

// 根据状态获取设备列表
export function glassesListByStatus(status: string, params?: PageQuery) {
  return securityHttp.get<Glasses>({ url: Api.glassesListByStatus, params: { ...params, status } });
}

// 获取设备选项列表
export function glassesOptionSelect(params?: any) {
  return securityHttp.get({ url: Api.glassesSelect, params });
}
