export interface Glasses {
  id: number; // 主键id
  deviceNo: string; // 设备标识id(设备中获得)
  model: string; // 型号
  factoryNo: string; // 出厂编号(设备外观设备编号)
  stationId: string; // 站点id
  stationName: string; // 站点名称
  status: string; // 状态：1:使用中(app使用中) 、2: 闲置(app未使用)、3:损坏
  remark?: string; // 备注
  createDept?: number; // 创建部门
  createBy?: number; // 创建者
  createTime?: Date; // 创建时间
  updateBy?: number; // 更新者
  updateTime?: Date; // 更新时间
  params?: { [key: string]: { [key: string]: any } }; // 请求参数
}
