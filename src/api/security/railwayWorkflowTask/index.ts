import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import {
  ControlTask,
  ArWorkflowDetailInfoVo2,
  WorkflowListResponseVo,
  GetWorkflowListParams,
  TaskStatisticsOverview,
} from './model';

enum Api {
  root = '/business/controlTask',
  controlTaskList = '/business/controlTask/list',
  controlTaskExport = '/business/controlTask/export',
  getStatisticsOverview = '/business/controlTask/getStatisticsOverview',
}

export function controlTaskList(params?: PageQuery) {
  return securityHttp.get<ControlTask>({ url: Api.controlTaskList, params });
}

export function controlTaskInfo(taskId: ID) {
  return securityHttp.get<ControlTask>({ url: Api.root + '/' + taskId });
}

export function controlTaskExport(data: any) {
  return securityCommonExport(Api.controlTaskExport, data);
}

export function controlTaskAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

export function controlTaskUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

export function controlTaskRemove(taskIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + taskIds });
}

// 获取任务报告数据
export function getTaskReport(params: { taskId: string; roleType: string; userId: string }) {
  return securityHttp.get<any>({ url: Api.root + '/report', params });
}

export function postControlTaskGetLiveVideoUrl(data = {}) {
  return securityHttp.post({
    url: '/business/controlTask/getLiveVideoUrl',
    data,
  });
}

// 获取任务相关执行人列表
export function getWorkflowListInfo(taskId: string) {
  return securityHttp.get<ArWorkflowDetailInfoVo2[]>({
    url: Api.root + '/getWorkflowListInfo',
    params: { taskId },
  });
}

// 获取某执行人下的流程信息列表
export function getWorkflowListByTaskIdAndPersonChargeId(data: GetWorkflowListParams) {
  return securityHttp.post<WorkflowListResponseVo>({
    url: Api.root + '/getWorkflowListByTaskIdAndPersonChargeId',
    data,
  });
}

// 获取任务统计概览
export function getTaskStatisticsOverview(params?: any) {
  return securityHttp.get<TaskStatisticsOverview>({ url: Api.getStatisticsOverview, params });
}
