import { securityHttp } from '@/utils/http/axios';
import { ID, IDS, PageQuery, securityCommonExport } from '@/api/base';
import { TableDataInfoSceneAnnexInfoVo } from './model';

enum Api {
  root = '/business/annexInfo',
  list = '/business/annexInfo/list',
  export = '/business/annexInfo/export',
}

export function getAnnexInfoList(params?: PageQuery) {
  return securityHttp.get<TableDataInfoSceneAnnexInfoVo>({ url: Api.list, params });
}

// 获取单个设备信息
export function getAnnexInfo(annexInfoId: ID) {
  return securityHttp.get<AnnexInfo>({ url: Api.root + '/' + annexInfoId });
}

// 导出设备信息
export function annexInfoExport(data: any) {
  return securityCommonExport(Api.export, data);
}

// 新增设备
export function annexInfoAdd(data: any) {
  return securityHttp.postWithMsg<void>({ url: Api.root, data });
}

// 更新设备信息
export function annexInfoUpdate(data: any) {
  return securityHttp.putWithMsg<void>({ url: Api.root, data });
}

// 删除设备
export function annexInfoRemove(annexInfoIds: IDS) {
  return securityHttp.deleteWithMsg({ url: Api.root + '/' + annexInfoIds });
}
