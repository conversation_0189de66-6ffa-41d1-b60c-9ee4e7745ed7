export interface AnnexInfo {
  /**
   * 附件类型
   */
  annexType?: string;
  /**
   * 附件地址
   */
  annexUrl?: string;
  /**
   * 创建者
   */
  createBy?: number;
  /**
   * 创建部门
   */
  createDept?: number;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 主键
   */
  id: number;
  /**
   * 排序的方向desc或者asc
   */
  isAsc?: string;
  /**
   * 排序列
   */
  orderByColumn?: string;
  /**
   * 当前页数
   */
  pageNum?: number;
  /**
   * 分页大小
   */
  pageSize?: number;
  /**
   * 请求参数
   */
  params?: { [key: string]: { [key: string]: any } };
  /**
   * 备注
   */
  remark?: string;
  /**
   * 场景id
   */
  sceneId: string;
  /**
   * 场景类型
   */
  sceneType: string;
  /**
   * 所属任务ID
   */
  taskId: string;
  /**
   * 任务类型
   */
  taskType: string;
  /**
   * 更新者
   */
  updateBy?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
}

export interface TableDataInfoSceneAnnexInfoVo {
  total: number;
  rows: AnnexInfo[];
  code: number;
  msg: string;
}
