export interface ControlTaskStep {
  arGlassId: string;
  createBy?: number;
  createDept?: number;
  createTime?: Date;
  durationMinutes?: number;
  endTime: Date;
  id: number;
  isAsc?: string;
  isMultipleAttachments?: string;
  orderByColumn?: string;
  orderNum: number;
  pageNum?: number;
  pageSize?: number;
  params?: { [key: string]: { [key: string]: any } };
  personChargeId: string;
  personChargeName: string;
  personChargeRoleType: string;
  preStepIds?: string;
  remark?: string;
  startTime: Date;
  status?: string;
  taskId: string;
  updateBy?: number;
  updateTime?: Date;
  workflowId: string;
  workflowInfo: string;
  workflowName: string;
}
