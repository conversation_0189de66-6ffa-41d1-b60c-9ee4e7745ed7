import { DescItem } from '@/components/Description';
import AnnexRender from './components/AnnexRender.vue';

export const descSchema: DescItem[] = [
  {
    label: 'Id唯一标识',
    field: 'id',
  },
  {
    label: '作业管控任务编码',
    field: 'taskId',
  },
  {
    label: '流程id',
    field: 'workflowId',
  },
  {
    label: '流程环节名称',
    field: 'workflowName',
  },
  {
    label: '流程环节内容',
    field: 'workflowInfo',
  },
  {
    label: '顺序',
    field: 'orderNum',
  },
  {
    label: '开始时间',
    field: 'startTime',
  },
  {
    label: '结束时间',
    field: 'endTime',
  },
  {
    label: '本项耗时（分钟）',
    field: 'durationMinutes',
  },
  {
    label: '执行人id',
    field: 'personChargeId',
  },
  {
    label: '执行人名称',
    field: 'personChargeName',
  },
  {
    label: '执行人角色',
    field: 'personChargeRoleType',
  },
  {
    label: '状态',
    field: 'status',
    render: (value) => {
      const statusMap = {
        '1': '未开始',
        '2': '进行中',
        '3': '已结束',
      };
      return statusMap[value] || value;
    },
  },
  {
    label: '是否允许附件多个',
    field: 'isMultipleAttachments',
    render: (value) => {
      const map = {
        '1': '是',
        '2': '否',
      };
      return map[value] || value;
    },
  },
  {
    label: '流程附件',
    field: 'annexUrl',
    render: (val) => <AnnexRender urls={val} />,
  },
  {
    label: '前置环节 ID',
    field: 'preStepIds',
  },
  {
    label: 'ar眼镜id',
    field: 'arGlassId',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建部门',
    field: 'createDept',
  },
  {
    label: '创建者',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '更新者',
    field: 'updateBy',
  },
  {
    label: '更新时间',
    field: 'updateTime',
  },
];
