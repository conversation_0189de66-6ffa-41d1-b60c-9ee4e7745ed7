<template>
  <div class="">
    <BasicTable
      @register="registerTable"
      :style="{ '--vben-basic-table-form-container-padding': 0 }"
      class=""
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <ControlTaskWorkflowListInfoModal @register="registerInfoModal" />
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { getControlTaskWorkflowList } from '@/api/security/railwayWorkflowTaskStep/index';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';
  import ControlTaskWorkflowListInfoModal from './InfoModal.vue';

  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  });

  defineOptions({ name: 'ControlTaskWorkflowList' });

  const [registerTable, { reload, selected }] = useTable({
    canResize: false,
    rowSelection: {
      type: 'checkbox',
    },
    inset: true,
    showTableSetting: false,
    // title: '子环节列表',
    showIndexColumn: false,
    api: (params) => {
      return getControlTaskWorkflowList({
        ...props.params,
        ...params,
      });
    },
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'controlTaskWorkflowList',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    openInfoModal(true, record);
  }
</script>

<style scoped></style>
