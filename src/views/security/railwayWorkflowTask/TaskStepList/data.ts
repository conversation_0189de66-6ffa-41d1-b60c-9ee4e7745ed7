import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  // { title: 'Id唯一标识', dataIndex: 'id' },
  // { title: '作业管控任务编码', dataIndex: 'taskId' },
  // { title: '流程id', dataIndex: 'workflowId' },
  { title: '流程环节名称', dataIndex: 'workflowName' },
  { title: '流程环节内容', dataIndex: 'workflowInfo' },
  { title: '顺序', dataIndex: 'orderNum' },
  { title: '开始时间', dataIndex: 'startTime' },
  { title: '结束时间', dataIndex: 'endTime' },
  { title: '本项耗时（分钟）', dataIndex: 'durationMinutes' },
  // { title: '执行人id', dataIndex: 'personChargeId' },
  { title: '执行人名称', dataIndex: 'personChargeName' },
  { title: '执行人角色', dataIndex: 'personChargeRoleType' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender({ value }) {
      const statusMap = {
        '1': '未开始',
        '2': '进行中',
        '3': '已结束',
      };
      return statusMap[value] || value;
    },
  },
  {
    title: '是否允许附件多个',
    dataIndex: 'isMultipleAttachments',
    customRender({ value }) {
      const map = {
        '1': '是',
        '2': '否',
      };
      return map[value] || value;
    },
  },
  // { title: '前置环节 ID', dataIndex: 'preStepIds' },
  // { title: 'ar眼镜id', dataIndex: 'arGlassId' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建部门', dataIndex: 'createDept' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '更新者', dataIndex: 'updateBy' },
  { title: '更新时间', dataIndex: 'updateTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '流程环节名称',
    field: 'workflowName',
    component: 'Input',
  },
  {
    label: '执行人角色',
    field: 'personChargeRoleType',
    component: 'Input',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: () => ({
      options: [
        { label: '未开始', value: '1' },
        { label: '进行中', value: '2' },
        { label: '已结束', value: '3' },
      ],
    }),
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键ID',
    component: 'Input',
    show: false,
  },
  {
    label: '作业管控任务编码',
    field: 'taskId',
    component: 'Input',
    required: true,
  },
  {
    label: '流程id',
    field: 'workflowId',
    component: 'Input',
    required: true,
  },
  {
    label: '流程环节名称',
    field: 'workflowName',
    component: 'Input',
    required: true,
  },
  {
    label: '流程环节内容',
    field: 'workflowInfo',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '顺序',
    field: 'orderNum',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '开始时间',
    field: 'startTime',
    component: 'DatePicker',
    required: true,
  },
  {
    label: '结束时间',
    field: 'endTime',
    component: 'DatePicker',
    required: true,
  },
  {
    label: '本项耗时（分钟）',
    field: 'durationMinutes',
    component: 'InputNumber',
  },
  {
    label: '执行人id',
    field: 'personChargeId',
    component: 'Input',
    required: true,
  },
  {
    label: '执行人名称',
    field: 'personChargeName',
    component: 'Input',
    required: true,
  },
  {
    label: '执行人角色',
    field: 'personChargeRoleType',
    component: 'Input',
    required: true,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未开始', value: '1' },
        { label: '进行中', value: '2' },
        { label: '已结束', value: '3' },
      ],
    },
    required: true,
  },
  {
    label: '是否允许附件多个',
    field: 'isMultipleAttachments',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: '1' },
        { label: '否', value: '2' },
      ],
    },
    required: true,
  },
  {
    label: '前置环节 ID',
    field: 'preStepIds',
    component: 'Input',
  },
  {
    label: 'ar眼镜id',
    field: 'arGlassId',
    component: 'Input',
    required: true,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createDept',
    label: '创建部门',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: '更新者',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'Input',
    show: false,
  },
];
