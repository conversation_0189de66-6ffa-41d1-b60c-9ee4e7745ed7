<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="作业管控任务子环节信息"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription">
      <template #annexUrl="{ model, field }">
        {{ model[field] }}
      </template>
    </Description>
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'ControlTaskInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(async (record) => {
    showSkeleton.value = true;
    if (!record) {
      return closeModal();
    }

    setDescProps({ data: record });
    showSkeleton.value = false;
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '160px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
