import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '任务编号',
    field: 'taskNum',
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
  },
  {
    label: '所属站点',
    field: 'stationName',
  },
  {
    label: '任务状态',
    field: 'status',
    render: (value) => {
      return renderDict(value, DictEnum.RAILWAY_TASK_STATUS);
    },
  },
  {
    label: '计划工作时间',
    field: 'plannedWorkingHours',
  },
  {
    label: '停电范围',
    field: 'powerOutageRange',
  },
  {
    label: '作业范围',
    field: 'scopeWork',
  },
  {
    label: '工作领导人',
    field: 'workLeader',
  },
  {
    label: '地线监护人',
    field: 'groundLineGuardian',
  },
  {
    label: '驻站联络人',
    field: 'stationLiaisonPerson',
  },
  {
    label: '工作组员',
    field: 'workTeam',
  },
  {
    label: '实际工作开始时间',
    field: 'startTime',
  },
  {
    label: '实际工作结束时间',
    field: 'endTime',
  },
  {
    label: '实际工作耗时（分钟）',
    field: 'durationMinutes',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建者',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];
