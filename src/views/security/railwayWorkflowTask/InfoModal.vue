<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="作业管控任务信息"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />

    <TaskStepList
      :key="currentTaskId"
      v-if="currentTaskId"
      class=""
      :params="{
        taskId: currentTaskId,
      }"
    />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { controlTaskInfo } from '@/api/security/railwayWorkflowTask';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';
  import TaskStepList from './TaskStepList/index.vue';

  defineOptions({ name: 'ControlTaskInfoModal' });

  const currentTaskId = ref();

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(async (taskId: string | number) => {
    showSkeleton.value = true;
    if (!taskId) {
      return closeModal();
    }
    const response = await controlTaskInfo(taskId);

    currentTaskId.value = taskId;
    // 赋值
    setDescProps({ data: response });
    showSkeleton.value = false;
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 3,
    labelStyle: {
      width: '160px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
