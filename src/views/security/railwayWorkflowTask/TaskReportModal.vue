<template>
  <BasicModal
    v-bind="$attrs"
    title="任务报告"
    :footer="null"
    @register="registerInnerModal"
    :width="1200"
  >
    <div class="task-report-modal">
      <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
        <a-tab-pane
          v-for="tab in tabList"
          :key="tab.personChargeId"
          :tab="`${tab.personChargeName} - ${translateRoleType(tab.personChargeRoleType)}`"
        >
          <div class="report-content">
            <a-spin :spinning="loading">
              <div v-if="currentReportData.length > 0" class="report-container">
                <!-- 报告头部信息 -->
                <div class="report-header">
                  <h2 class="report-title">流程执行情况</h2>
                  <div class="report-meta">
                    <span class="report-date"
                      >{{ currentReportInfo?.personName || '' }} -
                      {{ translateRoleType(currentReportInfo?.personChargeRoleType || '') }}</span
                    >
                  </div>
                </div>

                <!-- 基本信息 -->
                <div class="basic-info" v-if="currentReportInfo">
                  <div class="info-row">
                    <span class="label">工作区域:</span>
                    <span class="value">{{ currentReportInfo.workArea || '____' }}</span>
                    <span class="label">工作票号:</span>
                    <span class="value">{{ currentReportInfo.workTicketNum || '____' }}</span>
                    <span class="label">施工日计划编号:</span>
                    <span class="value">{{
                      currentReportInfo.constructionDayPlanNum || '____'
                    }}</span>
                  </div>
                </div>

                <!-- 流程信息表格 -->
                <div class="report-table">
                  <table class="control-table">
                    <thead>
                      <tr>
                        <th width="40">序号</th>
                        <th width="150">流程名称</th>
                        <th>流程环节内容</th>
                        <th width="120">开始时间</th>
                        <th width="120">结束时间</th>
                        <th width="80">状态</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in currentReportData" :key="item.id">
                        <td class="text-center">{{ item.orderNum || index + 1 }}</td>
                        <td class="text-center">{{ item.workflowName }}</td>
                        <td class="requirement">{{ item.workflowInfo }}</td>
                        <td class="text-center">
                          <span v-if="item.startTime" class="time-mark">{{ item.startTime }}</span>
                          <span v-else class="empty-mark">____</span>
                        </td>
                        <td class="text-center">
                          <span v-if="item.endTime" class="time-mark">{{ item.endTime }}</span>
                          <span v-else class="empty-mark">____</span>
                        </td>
                        <td class="text-center">
                          <span v-if="item.status === '3'" class="completed-mark">已完成</span>
                          <span v-else-if="item.status === '2'" class="progress-mark">进行中</span>
                          <span v-else-if="item.status === '1'" class="pending-mark">未开始</span>
                          <span v-else class="empty-mark">____</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <!-- 注意事项 -->
                <div class="notes">
                  <p
                    >注：此表显示当前执行人的流程执行情况，包括流程名称、环节内容、结束时间等信息。</p
                  >
                </div>
              </div>
              <div v-else class="no-data">
                <a-empty description="暂无流程数据" />
              </div>
            </a-spin>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { ref, computed } from 'vue';
  import {
    Tabs as ATabs,
    TabPane as ATabPane,
    Spin as ASpin,
    Empty as AEmpty,
  } from 'ant-design-vue';
  import type { Key } from 'ant-design-vue/es/_util/type';
  import {
    getWorkflowListInfo,
    getWorkflowListByTaskIdAndPersonChargeId,
  } from '@/api/security/railwayWorkflowTask/index';
  import type {
    ArWorkflowDetailInfoVo2,
    WorkflowListResponseVo,
  } from '@/api/security/railwayWorkflowTask/model';

  defineOptions({ name: 'TaskReportModal' });

  const loading = ref(false);
  const tabLoading = ref(false);
  const activeTabKey = ref('');
  const currentTaskRecord = ref<any>(null);
  const reportDataMap = ref<Record<string, WorkflowListResponseVo>>({});
  const tabList = ref<ArWorkflowDetailInfoVo2[]>([]);

  const [registerInnerModal, { closeModal }] = useModalInner(async (record: any) => {
    if (!record) {
      return closeModal();
    }

    currentTaskRecord.value = record;

    // 加载 tab 数据
    await loadTabList(record.id);
  });

  // 加载 tab 列表
  async function loadTabList(taskId: string) {
    tabLoading.value = true;
    try {
      const result = await getWorkflowListInfo(taskId);
      tabList.value = result || [];

      // 设置默认激活的tab
      if (tabList.value.length > 0) {
        activeTabKey.value = tabList.value[0].personChargeId;
        await loadReportData(activeTabKey.value);
      }
    } catch (error) {
      console.error('加载 tab 列表失败:', error);
      tabList.value = [];
    } finally {
      tabLoading.value = false;
    }
  }

  // 当前报告数据
  const currentReportData = computed(() => {
    const data = reportDataMap.value[activeTabKey.value];
    return data?.workflowList || [];
  });

  // 当前报告基本信息
  const currentReportInfo = computed(() => {
    return reportDataMap.value[activeTabKey.value] || null;
  });

  // 角色类型翻译
  const roleTypeMap = {
    leader: '工作领导人',
    contact: '驻站联络人',
    guardian: '地线监护人',
  };

  // 翻译角色类型
  function translateRoleType(roleType: string): string {
    return roleTypeMap[roleType as keyof typeof roleTypeMap] || roleType;
  }

  // 切换tab时加载对应数据
  function handleTabChange(key: Key) {
    const keyStr = String(key);
    activeTabKey.value = keyStr;
    loadReportData(keyStr);
  }

  // 加载报告数据
  async function loadReportData(personChargeId: string) {
    if (reportDataMap.value[personChargeId]) {
      return; // 已加载过的数据直接返回
    }

    loading.value = true;
    try {
      if (!currentTaskRecord.value?.id) return;

      const result = await getWorkflowListByTaskIdAndPersonChargeId({
        taskId: currentTaskRecord.value.id,
        personChargeId: personChargeId,
      });

      reportDataMap.value[personChargeId] = result || {
        workArea: '',
        workTicketNum: '',
        personName: '',
        personChargeRoleType: '',
        workflowList: [],
      };
    } catch (error) {
      console.error('加载报告数据失败:', error);
      reportDataMap.value[personChargeId] = {
        workArea: '',
        workTicketNum: '',
        personName: '',
        personChargeRoleType: '',
        workflowList: [],
      };
    } finally {
      loading.value = false;
    }
  }
</script>

<style scoped>
  .task-report-modal {
    .report-content {
      min-height: 600px;
    }

    .report-container {
      background: white;
      font-family: SimSun, serif;
    }

    .report-header {
      margin-bottom: 20px;
      text-align: center;

      .report-title {
        margin: 0 0 10px;
        font-size: 18px;
        font-weight: bold;
      }

      .report-meta {
        .report-date {
          color: #666;
          font-size: 14px;
        }
      }
    }

    .basic-info {
      margin-bottom: 20px;

      .info-row {
        display: flex;
        align-items: center;
        gap: 20px;
        font-size: 14px;

        .label {
          font-weight: bold;
        }

        .value {
          min-width: 100px;
          padding: 2px 5px;
          border-bottom: 1px solid #000;
        }
      }
    }

    .control-table {
      width: 100%;
      margin-bottom: 20px;
      border-collapse: collapse;

      th,
      td {
        padding: 8px;
        border: 1px solid #000;
        font-size: 12px;
        line-height: 1.4;
        text-align: left;
      }

      th {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: center;
      }

      .text-center {
        text-align: center;
      }

      .requirement {
        font-size: 11px;
        line-height: 1.3;
      }

      .completed-mark {
        color: #52c41a;
        font-size: 12px;
        font-weight: bold;
      }

      .progress-mark {
        color: #1890ff;
        font-size: 12px;
        font-weight: bold;
      }

      .pending-mark {
        color: #faad14;
        font-size: 12px;
        font-weight: bold;
      }

      .time-mark {
        color: #1890ff;
        font-size: 11px;
        font-weight: bold;
      }

      .empty-mark {
        color: #999;
      }
    }

    .signature-area {
      margin: 20px 0;

      .signature-row {
        display: flex;
        align-items: center;
        gap: 20px;
        font-size: 14px;

        .signature-label {
          font-weight: bold;
        }

        .signature-value {
          min-width: 80px;
          padding: 2px 5px;
          border-bottom: 1px solid #000;
          text-align: center;
        }
      }
    }

    .notes {
      margin-top: 20px;
      color: #666;
      font-size: 12px;
      line-height: 1.4;
    }

    .no-data {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }
  }
</style>
