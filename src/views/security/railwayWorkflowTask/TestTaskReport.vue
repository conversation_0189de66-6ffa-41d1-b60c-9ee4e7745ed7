<template>
  <div class="test-task-report">
    <div class="test-header">
      <h2>任务报告功能测试</h2>
      <p>点击下面的按钮测试任务报告弹窗功能</p>
    </div>

    <div class="test-content">
      <div class="test-data-section">
        <h3>测试数据</h3>
        <div class="data-display">
          <pre>{{ JSON.stringify(testTaskData, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-actions">
        <h3>测试操作</h3>
        <a-space>
          <a-button type="primary" @click="openTaskReport"> 打开任务报告 </a-button>
          <a-button @click="resetTestData"> 重置测试数据 </a-button>
          <a-button @click="addMoreGuardians"> 添加更多监护人 </a-button>
        </a-space>
      </div>

      <div class="test-instructions">
        <h3>测试说明</h3>
        <ul>
          <li>点击"打开任务报告"按钮会弹出报告查看弹窗</li>
          <li
            >弹窗顶部会显示多个Tab，对应不同角色：
            <ul>
              <li>负责人 - 基于 workLeaderId</li>
              <li>联络人 - 基于 stationLiaisonPersonId</li>
              <li>地线监护人-{姓名} - 基于 groundLineGuardianId 和 groundLineGuardianNames</li>
            </ul>
          </li>
          <li>每个Tab显示对应角色的报告内容</li>
          <li>报告内容包括13个标准作业项目的控制表</li>
          <li>可以切换Tab查看不同角色的报告数据</li>
        </ul>
      </div>
    </div>

    <!-- 任务报告弹窗 -->
    <TaskReportModal width="90%" @register="registerTaskReportModal" />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Button as AButton, Space as ASpace } from 'ant-design-vue';
  import { useModal } from '@/components/Modal';
  import TaskReportModal from './TaskReportModal.vue';
  import { mockTaskData } from './test-data';

  defineOptions({ name: 'TestTaskReport' });

  const testTaskData = ref({ ...mockTaskData });

  const [registerTaskReportModal, { openModal: openTaskReportModal }] = useModal();

  function openTaskReport() {
    openTaskReportModal(true, testTaskData.value);
  }

  function resetTestData() {
    testTaskData.value = { ...mockTaskData };
  }

  function addMoreGuardians() {
    testTaskData.value = {
      ...testTaskData.value,
      groundLineGuardian: '王监护,赵监护,孙监护,李监护,陈监护',
      groundLineGuardianId: 'U003,U004,U005,U006,U007',
      groundLineGuardianNames: '王师傅,赵师傅,孙师傅,李师傅,陈师傅',
    };
  }
</script>

<style scoped>
  .test-task-report {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .test-header {
    margin-bottom: 30px;
    text-align: center;

    h2 {
      margin-bottom: 10px;
      color: #1890ff;
    }

    p {
      color: #666;
      font-size: 14px;
    }
  }

  .test-content {
    display: grid;
    gap: 30px;
  }

  .test-data-section {
    h3 {
      margin-bottom: 15px;
      padding-bottom: 5px;
      border-bottom: 2px solid #1890ff;
      color: #333;
    }

    .data-display {
      padding: 15px;
      overflow-x: auto;
      border-radius: 6px;
      background: #f5f5f5;

      pre {
        margin: 0;
        color: #333;
        font-size: 12px;
        line-height: 1.4;
      }
    }
  }

  .test-actions {
    h3 {
      margin-bottom: 15px;
      padding-bottom: 5px;
      border-bottom: 2px solid #52c41a;
      color: #333;
    }
  }

  .test-instructions {
    h3 {
      margin-bottom: 15px;
      padding-bottom: 5px;
      border-bottom: 2px solid #faad14;
      color: #333;
    }

    ul {
      color: #666;
      line-height: 1.6;

      li {
        margin-bottom: 8px;
      }

      ul {
        margin-top: 5px;

        li {
          margin-bottom: 4px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .test-task-report {
      padding: 15px;
    }

    .test-content {
      gap: 20px;
    }

    .data-display pre {
      font-size: 10px;
    }
  }
</style>
