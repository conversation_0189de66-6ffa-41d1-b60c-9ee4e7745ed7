<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="录播列表"
    :footer="null"
    @register="registerInnerModal"
  >
    <a-tabs v-model:activeKey="tabActiveKey" class="-mt-4">
      <a-tab-pane :key="item.value" :tab="item.label" v-for="item of tabPanes">
        <RecordedBroadcast
          v-if="activeTaskId"
          :params="{
            taskId: activeTaskId,
            roleType: tabActiveKey,
          }"
        />
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { ref } from 'vue';
  import RecordedBroadcast from '@/views/security/components/RecordedBroadcast/index.vue';
  import { Tabs as ATabs, TabPane as ATabPane } from 'ant-design-vue';

  defineOptions({ name: 'RecordedBroadcastInfoModal' });

  const activeTaskId = ref();
  const [registerInnerModal, { closeModal }] = useModalInner(async (taskId: string | number) => {
    if (!taskId) {
      return closeModal();
    }

    activeTaskId.value = taskId;
  });

  const tabPanes = ref([
    { label: '工作领导人', value: 'leader' },
    { label: '驻站联络人', value: 'contact' },
    { label: '地线监护人', value: 'guardian' },
  ]);

  const tabActiveKey = ref('leader');
</script>

<style scoped></style>
