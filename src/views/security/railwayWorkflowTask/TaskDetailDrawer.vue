<template>
  <BasicDrawer
    v-bind="$attrs"
    :title="drawerTitle"
    :width="470"
    :footer="null"
    @register="registerDrawer"
  >
    <div class="h-full flex flex-col -mt-4">
      <!-- 标签页导航 -->
      <div class="flex-none sticky -top-4 bg-white z-50">
        <Tabs
          v-model:activeKey="activeTabKey"
          centered
          :tabBarGutter="100"
          class=""
          @change="handleTabChange"
        >
          <TabPane key="taskInfo" tab="任务信息" />
          <TabPane key="workflow" tab="流程环节" />
          <TabPane key="monitor" tab="监控画面" />
        </Tabs>
      </div>

      <!-- 标签页内容 -->
      <div class="flex-1 overflow-hidden">
        <!-- 任务信息标签页 -->
        <div v-show="activeTabKey === 'taskInfo'" class="h-full overflow-auto">
          <Description
            v-if="taskData && !showSkeleton"
            :column="1"
            :data="taskData"
            :schema="descSchema"
            :labelStyle="{
              width: '160px',
              minWidth: '150px',
            }"
            :bordered="false"
          />
          <Skeleton v-if="showSkeleton" active :paragraph="{ rows: 8 }" />
        </div>

        <!-- 流程环节标签页 -->
        <div v-if="activeTabKey === 'workflow'" class="h-full overflow-hidden">
          <div v-if="currentTaskId" class="h-full">
            <WorkflowTimeline
              :key="currentTaskId"
              :params="{
                taskId: currentTaskId,
              }"
              class="h-full"
            />
          </div>
          <div v-else class="h-full flex items-center justify-center text-gray-400">
            <span>暂无流程环节数据</span>
          </div>
        </div>

        <!-- 监控画面标签页 -->
        <div v-if="activeTabKey === 'monitor'" class="h-full overflow-hidden">
          <MonitorScreen
            v-if="currentTaskId"
            :task-id="currentTaskId"
            class="h-full"
          />
          <div v-else class="h-full flex items-center justify-center text-gray-400">
            <span>暂无监控画面数据</span>
          </div>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { Description } from '@/components/Description';
  import { Tabs, TabPane, Skeleton } from 'ant-design-vue';
  import { controlTaskInfo } from '@/api/security/railwayWorkflowTask';
  import { descSchema } from './info';
  import WorkflowTimeline from './components/WorkflowTimeline.vue';
  import MonitorScreen from './components/MonitorScreen.vue';

  defineOptions({ name: 'TaskDetailDrawer' });

  // 响应式数据
  const activeTabKey = ref('taskInfo');
  const currentTaskId = ref<string | number>();
  const taskData = ref<any>();
  const showSkeleton = ref(false);
  const drawerTitle = ref('详情');

  // 抽屉注册
  const [registerDrawer, { closeDrawer }] = useDrawerInner(async (taskId: string | number) => {
    if (!taskId) {
      return closeDrawer();
    }

    showSkeleton.value = true;
    currentTaskId.value = taskId;

    try {
      // 获取任务详情
      const response = await controlTaskInfo(taskId);
      taskData.value = response;
      drawerTitle.value = '详情';
    } catch (error) {
      console.error('加载任务详情失败:', error);
    } finally {
      showSkeleton.value = false;
    }
  });

  // 标签页切换
  function handleTabChange(key: string) {
    activeTabKey.value = key;
  }
</script>

<style scoped>
.ant-tabs-content-holder {
  height: 100%;
}

.ant-tabs-tabpane {
  height: 100%;
}
</style>
