// 测试数据 - 用于演示任务报告功能
export const mockTaskData = {
  id: 1,
  taskNum: 'ZYGK-202412210930',
  constructionDayPlanNum: 'SGRC-20241221-001',
  stationId: '1001',
  stationName: '上海站',
  status: '2', // 已完成
  plannedWorkingHours: '09:00-15:00',
  powerOutageRange: '2#接触网(站/区间) 277#',
  scopeWork: '地线线操作，共计2组',

  // 人员信息
  workLeader: '张工程师',
  workLeaderId: 'U001',

  stationLiaisonPerson: '李联络员',
  stationLiaisonPersonId: 'U002',

  groundLineGuardian: '王监护,赵监护,孙监护',
  groundLineGuardianId: 'U003,U004,U005',
  groundLineGuardianNames: '王师傅,赵师傅,孙师傅',

  workTeam: '作业组A',

  startTime: new Date('2024-12-21 09:00:00'),
  endTime: new Date('2024-12-21 15:30:00'),
  durationMinutes: 390,

  remark: '作业顺利完成，无异常情况',
  createBy: 1001,
  createTime: new Date('2024-12-20 16:00:00'),
};

// 模拟报告数据
export const mockReportData = {
  leader: {
    workshop: '电气工区',
    guardianName: '张工程师',
    workTicketNo: 'LEADER-20241221001',
    operator: '操作员A',
    guardian: '张工程师',
    roleType: 'leader',
    userId: 'U001',
  },
  contact: {
    workshop: '电气工区',
    guardianName: '李联络员',
    workTicketNo: 'CONTACT-20241221001',
    operator: '操作员B',
    guardian: '李联络员',
    roleType: 'contact',
    userId: 'U002',
  },
  guardian1: {
    workshop: '电气工区',
    guardianName: '王师傅',
    workTicketNo: 'GUARDIAN-20241221001',
    operator: '操作员C',
    guardian: '王师傅',
    roleType: 'guardian',
    userId: 'U003',
  },
  guardian2: {
    workshop: '电气工区',
    guardianName: '赵师傅',
    workTicketNo: 'GUARDIAN-20241221002',
    operator: '操作员D',
    guardian: '赵师傅',
    roleType: 'guardian',
    userId: 'U004',
  },
  guardian3: {
    workshop: '电气工区',
    guardianName: '孙师傅',
    workTicketNo: 'GUARDIAN-20241221003',
    operator: '操作员E',
    guardian: '孙师傅',
    roleType: 'guardian',
    userId: 'U005',
  },
};

// 使用说明
export const usageInstructions = `
## 如何测试任务报告功能

### 1. 准备测试数据
确保任务记录包含以下字段：
- workLeaderId: 工作领导人ID
- stationLiaisonPersonId: 驻站联络人ID  
- groundLineGuardianId: 地线监护人ID（多个用逗号分隔）
- groundLineGuardianNames: 地线监护人姓名（多个用逗号分隔）

### 2. 测试步骤
1. 在任务列表页面找到任务行
2. 点击"任务报告"按钮
3. 弹窗打开后会显示多个Tab：
   - 负责人 (基于workLeaderId)
   - 联络人 (基于stationLiaisonPersonId)  
   - 地线监护人-王师傅 (基于groundLineGuardianId[0])
   - 地线监护人-赵师傅 (基于groundLineGuardianId[1])
   - 地线监护人-孙师傅 (基于groundLineGuardianId[2])

### 3. 验证功能
- Tab切换是否正常
- 报告内容是否正确显示
- 加载状态是否正常
- 弹窗关闭是否正常

### 4. 数据格式示例
\`\`\`javascript
const taskRecord = {
  id: 1,
  workLeaderId: 'U001',
  stationLiaisonPersonId: 'U002', 
  groundLineGuardianId: 'U003,U004,U005',
  groundLineGuardianNames: '王师傅,赵师傅,孙师傅',
  // ... 其他字段
};
\`\`\`
`;
