<template>
  <div class="person-history-video">
    <!-- 人员标签 -->
    <div class="person-label">
      {{ personName }}
    </div>

    <!-- 主监控画面 -->
    <div class="main-monitor">
      <div v-if="selectedVideo" class="main-video-container">
        <video
          :src="selectedVideo.fileUrl"
          controls
          class="main-video"
          @loadedmetadata="handleVideoLoaded"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
      <div v-else class="main-video-placeholder">
        <div class="placeholder-content">
          <PlayCircleOutlined class="placeholder-icon" />
          <div class="placeholder-text">选择历史录像进行播放</div>
        </div>
      </div>
    </div>

    <!-- 历史录像缩略图 -->
    <div class="history-thumbnails-container">
      <!-- 缩略图列表 -->
      <div
        ref="thumbnailsRef"
        class="history-thumbnails"
      >
        <div
          v-for="(video, index) in videos"
          :key="video.id"
          :ref="el => setThumbnailRef(el, index)"
          class="history-thumbnail"
          :class="{ active: selectedVideo?.id === video.id }"
          @click="handleVideoSelect(video, index)"
        >
          <div class="thumbnail-wrapper">
            <!-- <img
              :src="video.thumbnailUrl || '/images/video-placeholder.png'"
              alt=""
              class="thumbnail-img"
            /> -->
            <!-- 播放按钮覆盖层 -->
            <div class="thumbnail-overlay">
              <PlayCircleOutlined class="overlay-icon" />
            </div>
            <!-- 视频序号 -->
            <div class="thumbnail-number">
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, nextTick } from 'vue';
  import { PlayCircleOutlined } from '@ant-design/icons-vue';

  interface RecordedVideo {
    id: string;
    fileName: string;
    fileUrl: string;
    thumbnailUrl?: string;
    createTime: string;
    taskId: string;
    personChargeId?: string;
    personChargeName?: string;
    personChargeRoleType?: string;
  }

  defineOptions({ name: 'PersonHistoryVideo' });

  const props = defineProps({
    personName: {
      type: String,
      required: true,
    },
    videos: {
      type: Array as () => RecordedVideo[],
      default: () => [],
    },
  });

  // 响应式数据
  const selectedVideo = ref<RecordedVideo | null>(null);
  const thumbnailsRef = ref<HTMLElement | null>(null);
  const thumbnailRefs = ref<(HTMLElement | null)[]>([]);

  // 监听视频列表变化
  watch(() => props.videos, (newVideos) => {
    if (newVideos.length > 0 && !selectedVideo.value) {
      selectedVideo.value = newVideos[0];
      // 自动滚动到第一个视频
      nextTick(() => {
        scrollToThumbnail(0);
      });
    }
  }, { immediate: true });

  // 设置缩略图引用
  function setThumbnailRef(el: HTMLElement | null, index: number) {
    if (el) {
      thumbnailRefs.value[index] = el;
    }
  }

  // 选择视频
  function handleVideoSelect(video: RecordedVideo, index: number) {
    selectedVideo.value = video;

    // 自动滚动到合适位置
    nextTick(() => {
      scrollToThumbnail(index);
    });
  }

  // 滚动到指定缩略图
  function scrollToThumbnail(index: number) {
    const thumbnailElement = thumbnailRefs.value[index];
    const containerElement = thumbnailsRef.value;

    if (thumbnailElement && containerElement) {
      // 计算目标滚动位置，使缩略图尽可能居中
      const thumbnailCenter = thumbnailElement.offsetLeft + thumbnailElement.offsetWidth / 2;
      const containerCenter = containerElement.clientWidth / 2;

      // 计算目标滚动位置
      let targetScrollLeft = thumbnailCenter - containerCenter;

      // 确保不会滚动超出边界
      const maxScrollLeft = containerElement.scrollWidth - containerElement.clientWidth;
      targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));

      // 平滑滚动到目标位置
      containerElement.scrollTo({
        left: targetScrollLeft,
        behavior: 'smooth'
      });
    }
  }

  // 视频加载完成
  function handleVideoLoaded() {
    // 可以在这里添加视频加载完成后的逻辑
  }
</script>

<style scoped lang="scss">
.person-history-video {
  @apply relative;

  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #1a1a1a;
}

.person-label {
  @apply bg-white/70 text-xs font-medium text-gray-700 absolute top-2 left-2 z-10 p-1 rounded;
}

.main-monitor {
  @apply relative;
  
  height: 240px;
  margin-bottom: 10px;
  overflow: hidden;
  border-radius: 8px;
  background-color: #000;
}

.main-video-container {
  @apply w-full h-full;
}

.main-video {
  @apply w-full h-full object-cover;
}

.main-video-placeholder {
  @apply w-full h-full flex items-center justify-center;
  
  background-color: #2a2a2a;
}

.placeholder-content {
  @apply text-center text-gray-400;
}

.placeholder-icon {
  @apply text-6xl mb-4;
}

.placeholder-text {
  @apply text-lg;
}

.history-thumbnails-container {
  @apply pb-3 px-3;

  margin: 0 auto;
}

.history-thumbnails {
  @apply flex gap-4;

  overflow: auto hidden;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.history-thumbnail {
  @apply cursor-pointer transition-all duration-200 flex-shrink-0;
  
  scroll-snap-align: center;

  &:hover {

    .thumbnail-number {
      @apply hidden;
    }
  }

  &.active {
    .thumbnail-wrapper {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 30%);
    }
  }
}

.thumbnail-wrapper {
  @apply relative bg-gray-900 rounded overflow-hidden;
  
  width: 70px;
  height: 48px;
  transition: all 0.2s ease;
  border: 1px solid #fff;
}

.thumbnail-img {
  @apply w-full h-full object-cover;
}

.thumbnail-overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity;
}

.overlay-icon {
  @apply text-white text-xl;
}

.thumbnail-number {
  @apply absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 bg-opacity-70 text-white text-lg text-shadow;
}
</style>
