<template>
  <div class="monitor-screen">
    <!-- 监控画面切换按钮 -->
    <div class="flex-none mb-4 flex justify-center">
      <a-radio-group 
        v-model:value="monitorMode" 
        button-style="solid"
        @change="handleModeChange"
      >
        <a-radio-button value="live">实时画面</a-radio-button>
        <a-radio-button value="record">历史视频</a-radio-button>
      </a-radio-group>
    </div>

    <!-- 监控画面内容 -->
    <div class="">
      <!-- 实时监控画面 -->
      <div v-if="monitorMode === 'live'" class="space-y-4">
        <div v-if="liveStreams.length > 0">
          <div 
            v-for="stream in liveStreams" 
            :key="stream.key"
            class="monitor-item pb-2"
          >
            <!-- 视频播放器 -->
            <div class="video-container relative rounded-md overflow-hidden">
              <LivePlayer
                :key="stream.key"
                :path="stream.path"
                class="h-full w-full"
              />

              <div class="person-label">
                {{ stream.title }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-state">
          <span>暂无实时监控画面</span>
        </div>
      </div>

      <!-- 历史录像 -->
      <div v-else-if="monitorMode === 'record'" class="record-layout">
        <div v-if="groupedRecordedVideos.length > 0" class="space-y-4">
          <PersonHistoryVideo
            v-for="group in groupedRecordedVideos"
            :key="group.personKey"
            :person-name="group.personName"
            :videos="group.videos"
          />
        </div>
        <div v-else class="empty-state">
          <span>暂无历史录像数据</span>
        </div>
      </div>
    </div>

    <!-- 视频预览弹窗 -->
    <a-modal
      v-model:open="previewVisible"
      title="视频预览"
      :width="800"
      :footer="null"
      centered
    >
      <div v-if="currentPreviewVideo" class="preview-container">
        <video
          :src="currentPreviewVideo.fileUrl"
          controls
          class="w-full h-auto"
          style="max-height: 500px;"
        >
          您的浏览器不支持视频播放
        </video>
        <div class="mt-2 text-gray-600">
          {{ currentPreviewVideo.fileName }}
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import { Radio as ARadio, Modal as AModal } from 'ant-design-vue';
  import { PlayCircleOutlined } from '@ant-design/icons-vue';
  import LivePlayer from '@/components/LivePlayer/index.vue';
  import PersonHistoryVideo from './PersonHistoryVideo.vue';
  import { postControlTaskGetLiveVideoUrl } from '@/api/security/railwayWorkflowTask';
  import { recordedBroadcastList } from '@/api/security/recordedBroadcast';
  import { formatToDateTime } from '@/utils/dateUtil';

  const ARadioGroup = ARadio.Group;
  const ARadioButton = ARadio.Button;

  defineOptions({ name: 'MonitorScreen' });

  interface StreamConfig {
    key: string;
    title: string;
    path: string;
    personChargeId: string;
    personChargeName: string;
    personChargeRoleType: string;
  }

  interface RecordedVideo {
    id: string;
    fileName: string;
    fileUrl: string;
    thumbnailUrl?: string;
    createTime: string;
    taskId: string;
    personChargeId?: string;
    personChargeName?: string;
    personChargeRoleType?: string;
  }

  interface PersonGroup {
    personKey: string;
    personName: string;
    videos: RecordedVideo[];
  }

  const props = defineProps({
    taskId: {
      type: [String, Number],
      required: true,
    },
  });

  // 响应式数据
  const monitorMode = ref<'live' | 'record'>('live');
  const liveStreams = ref<StreamConfig[]>([]);
  const groupedRecordedVideos = ref<PersonGroup[]>([]);
  const previewVisible = ref(false);
  const currentPreviewVideo = ref<RecordedVideo | null>(null);



  // 监听任务ID变化
  watch(() => props.taskId, (newTaskId) => {
    if (newTaskId) {
      loadData();
    }
  }, { immediate: true });

  // 加载数据
  async function loadData() {
    // 先加载实时流数据，然后基于实时流数据加载历史录像
    await loadLiveStreams();
    await loadRecordedVideos();
  }

  // 加载实时监控流
  async function loadLiveStreams() {
    try {
      const data = await postControlTaskGetLiveVideoUrl({ taskId: props.taskId });
      
      const roleMap = {
        leader: '工作领导人',
        contact: '驻站联络人',
        guardian: '地线监护人',
      };

      const streams = data
        .filter((item: any) => item.arGlassId)
        .map((item: any) => ({
          key: item.personChargeRoleType + item.personChargeId + item.arGlassId,
          title: `${item.personChargeName} - ${roleMap[item.personChargeRoleType]}`,
          path: `/${props.taskId}/${item.arGlassId}/${item.personChargeRoleType}/${item.personChargeId}`,
          personChargeId: item.personChargeId,
          personChargeName: item.personChargeName,
          personChargeRoleType: item.personChargeRoleType,
        }));

      liveStreams.value = streams;
    } catch (error) {
      console.error('加载实时监控流失败:', error);
      liveStreams.value = [];
    }
  }

  // 加载历史录像
  async function loadRecordedVideos() {
    try {
      const groups: PersonGroup[] = [];

      // 为每个人员分别获取历史视频
      for (const stream of liveStreams.value) {
        try {
          const response = await recordedBroadcastList({
            taskId: props.taskId,
            userId: stream.personChargeId, // 使用 userId 参数
            pageNum: 1,
            pageSize: 1000,
          });

          const videos = response.rows || [];

          if (videos.length > 0) {
            groups.push({
              personKey: stream.personChargeId,
              personName: `${stream.personChargeName} - ${getRoleDisplayName(stream.personChargeRoleType)}`,
              videos: videos,
            });
          }
        } catch (error) {
          console.error(`加载 ${stream.personChargeName} 的历史录像失败:`, error);
        }
      }

      groupedRecordedVideos.value = groups;
    } catch (error) {
      console.error('加载历史录像失败:', error);
      groupedRecordedVideos.value = [];
    }
  }

  // 获取角色显示名称
  function getRoleDisplayName(roleType: string): string {
    const roleMap = {
      leader: '工作领导人',
      contact: '驻站联络人',
      guardian: '地线监护人',
    };
    return roleMap[roleType] || roleType;
  }

  // 模式切换处理
  function handleModeChange() {
    // 可以在这里添加切换时的逻辑
  }

  // 视频预览
  function handleVideoPreview(video: RecordedVideo) {
    currentPreviewVideo.value = video;
    previewVisible.value = true;
  }

  // 格式化时间
  function formatTime(time: string) {
    return formatToDateTime(time);
  }



  onMounted(() => {
    if (props.taskId) {
      loadData();
    }
  });
</script>

<style scoped lang="scss">
.monitor-screen {
  padding: 0;
}

.monitor-item {
  @apply bg-white rounded-lg overflow-hidden shadow-sm;
}

.person-label {
  @apply bg-white/70 text-xs font-medium text-gray-700 absolute top-2 left-2 z-10 p-1 rounded;
}

.video-container {
  @apply bg-black;

  height: 250px;
}

// 历史录像布局样式
.record-layout {
  @apply relative;
}

// 保留原有样式
.person-group {
  @apply bg-white rounded-lg overflow-hidden shadow-sm border;
}

.video-grid {
  @apply grid grid-cols-4 gap-3 p-4;
}

.video-thumbnail {
  @apply cursor-pointer transition-transform hover:scale-105;
}

.thumbnail-container {
  @apply relative bg-gray-900 rounded overflow-hidden;

  aspect-ratio: 16/9;
}

.thumbnail-image {
  @apply w-full h-full object-cover;
}

.play-overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity;
}

.play-icon {
  @apply text-white text-2xl;
}

.video-number {
  @apply absolute top-2 left-2 bg-black bg-opacity-70 text-white text-xs px-1.5 py-0.5 rounded;
}

.video-info {
  @apply mt-2;
}

.video-title {
  @apply text-sm font-medium text-gray-900 truncate;
}

.video-time {
  @apply text-xs text-gray-500 mt-1;
}

.empty-state {
  @apply h-full flex items-center justify-center text-gray-400;
}

.preview-container {
  @apply w-full;
}
</style>
