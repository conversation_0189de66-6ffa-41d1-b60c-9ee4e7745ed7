import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
import { stationOptionSelect } from '@/api/business/station';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '任务编号', dataIndex: 'taskNum' },
  { title: '施工日计划编号', dataIndex: 'constructionDayPlanNum' },
  { title: '所属站点', dataIndex: 'stationName' },
  {
    title: '任务状态',
    dataIndex: 'status',
    customRender({ value }) {
      return renderDict(value, DictEnum.RAILWAY_TASK_STATUS);
    },
  },
  { title: '计划工作时间', dataIndex: 'plannedWorkingHours' },
  { title: '停电范围', dataIndex: 'powerOutageRange' },
  { title: '作业范围', dataIndex: 'scopeWork' },
  { title: '工作领导人', dataIndex: 'workLeader' },
  { title: '地线监护人', dataIndex: 'groundLineGuardian' },
  { title: '驻站联络人', dataIndex: 'stationLiaisonPerson' },
  { title: '工作组员', dataIndex: 'workTeam' },
  { title: '实际工作开始时间', dataIndex: 'startTime' },
  { title: '实际工作结束时间', dataIndex: 'endTime' },
  { title: '实际工作耗时（分钟）', dataIndex: 'durationMinutes' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '任务编号',
    field: 'taskNum',
    component: 'Input',
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
    component: 'Input',
  },
  {
    label: '所属站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.RAILWAY_TASK_STATUS),
    },
  },
  {
    label: '计划工作时间',
    field: 'plannedWorkingHours',
    component: 'Input',
  },
  {
    label: '停电范围',
    field: 'powerOutageRange',
    component: 'Input',
  },
  {
    label: '作业范围',
    field: 'scopeWork',
    component: 'Input',
  },
  {
    label: '工作领导人',
    field: 'workLeader',
    component: 'Input',
  },
  {
    label: '地线监护人',
    field: 'groundLineGuardian',
    component: 'Input',
  },
  {
    label: '驻站联络人',
    field: 'stationLiaisonPerson',
    component: 'Input',
  },
  {
    label: '工作组员',
    field: 'workTeam',
    component: 'Input',
  },
  {
    label: '实际工作开始时间',
    field: 'startTime',
    component: 'RangePicker',
  },
  {
    label: '实际工作结束时间',
    field: 'endTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '任务编号(Id唯一标识)',
    component: 'Input',
    show: false,
  },
  {
    label: '任务编号',
    field: 'taskNum',
    component: 'Input',
    required: true,
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
    component: 'Input',
    required: true,
  },
  {
    label: '所属站点',
    field: 'stationId',
    slot: 'stationId',
    defaultValue: Number(localStorage.getItem('stationId')),
    required: true,
  },
  {
    label: '所属站点名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '执行中', value: '1' },
        { label: '已完成', value: '2' },
        { label: '异常结束', value: '3' },
      ],
    },
    required: true,
  },
  {
    label: '计划工作时间',
    field: 'plannedWorkingHours',
    component: 'Input',
    required: true,
  },
  {
    label: '停电范围',
    field: 'powerOutageRange',
    component: 'Input',
    required: true,
  },
  {
    label: '作业范围',
    field: 'scopeWork',
    component: 'Input',
    required: true,
  },
  {
    label: '地线监护人',
    field: 'groundLineGuardian',
    component: 'Input',
    required: true,
  },
  {
    label: '地线监护人id(可多个逗号分割)',
    field: 'groundLineGuardianId',
    component: 'Input',
    required: true,
  },
  {
    label: '驻站联络人',
    field: 'stationLiaisonPerson',
    component: 'Input',
    required: true,
  },
  {
    label: '驻站联络人id',
    field: 'stationLiaisonPersonId',
    component: 'Input',
    required: true,
  },
  {
    label: '工作领导人',
    field: 'workLeader',
    component: 'Input',
    required: true,
  },
  {
    label: '工作领导人id',
    field: 'workLeaderId',
    component: 'Input',
    required: true,
  },
  {
    label: '工作组员',
    field: 'workTeam',
    component: 'Input',
    required: true,
  },
  {
    label: '实际工作开始时间',
    field: 'startTime',
    component: 'DatePicker',
  },
  {
    label: '实际工作结束时间',
    field: 'endTime',
    component: 'DatePicker',
  },
  {
    label: '实际工作耗时（分钟）',
    field: 'durationMinutes',
    component: 'InputNumber',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createDept',
    label: '创建部门',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: '更新者',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'params',
    label: '请求参数',
    component: 'Input',
    show: false,
  },
];
