import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '检修点编号', dataIndex: 'pointNum' },
  { title: '检修点名称', dataIndex: 'pointName' },
  { title: '顺序', dataIndex: 'orderNum' },
  { title: '位置描述', dataIndex: 'locationDesc' },
  { title: '检查标准及方法', dataIndex: 'checkStandard' },
  { title: '创建人', dataIndex: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '检修点编号',
    field: 'pointNum',
    component: 'Input',
  },
  {
    label: '检修点名称',
    field: 'pointName',
    component: 'Input',
  },
  {
    label: '顺序',
    field: 'orderNum',
    component: 'InputNumber',
  },
  {
    label: '位置描述',
    field: 'locationDesc',
    component: 'Input',
  },
  {
    label: '检查标准及方法',
    field: 'checkStandard',
    component: 'Input',
  },
  {
    label: '创建人',
    field: 'createUser',
    component: 'Input',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键ID',
    component: 'Input',
    show: false,
  },
  {
    label: '检修点编号',
    field: 'pointNum',
    component: 'Input',
    required: true,
  },
  {
    label: '检修点名称',
    field: 'pointName',
    component: 'Input',
    required: true,
  },
  {
    label: '顺序',
    field: 'orderNum',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '位置描述',
    field: 'locationDesc',
    component: 'InputTextArea',
  },
  {
    label: '检查标准及方法',
    field: 'checkStandard',
    component: 'InputTextArea',
    required: true,
  },
  {
    field: 'itemId',
    label: '所属检修项编号',
    component: 'Input',
    show: false,
  },
  {
    field: 'createUser',
    label: '创建人',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
