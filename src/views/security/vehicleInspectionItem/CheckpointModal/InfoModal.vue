<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="检查点详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 10 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { inspectionPointInfo } from '@/api/security/vehicleInspectionPoint';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'InspectionPointInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(async (inspectionPointId: number) => {
    showSkeleton.value = true;
    if (!inspectionPointId) return closeModal();

    const response = await inspectionPointInfo(inspectionPointId);
    setDescProps({ data: response });
    showSkeleton.value = false;
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: { width: '180px', minWidth: '180px' },
    schema: descSchema,
  });
</script>

<style scoped>
  .ant-descriptions-item-label {
    width: 180px !important;
  }
</style>
