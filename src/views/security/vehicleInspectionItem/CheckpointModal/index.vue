<template>
  <BasicModal v-bind="$attrs" :title="title" @register="registerInnerModal" @cancel="closeModal">
    <BasicTable
      @register="registerTable"
      :style="{ '--vben-basic-table-form-container-padding': 0 }"
    >
      <template #toolbar>
        <Space>
          <a-button type="primary" @click="handleAdd">新增</a-button>
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(inspectionPointRemove)"
            :disabled="!selected"
            >删除</a-button
          >
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '修改',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <InspectionPointModal @register="registerPointModal" @reload="reload" />
    <InspectionPointInfoModal @register="registerPointInfoModal" @reload="reload" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    inspectionPointList,
    inspectionPointRemove,
  } from '@/api/security/vehicleInspectionPoint';
  import InspectionPointModal from './Modal.vue';
  import InspectionPointInfoModal from './InfoModal.vue';
  import { columns, formSchemas } from './data';
  import { IconEnum } from '@/enums/appEnum';

  import { ref } from 'vue';

  defineOptions({ name: 'InspectionPointListModal' });

  const title = ref<string>('检查点管理');
  let currentInspectionItemId = ref<number | null>(null);

  const [registerInnerModal, { closeModal }] = useModalInner(async (inspectionItemId: number) => {
    currentInspectionItemId.value = inspectionItemId;
    await reload();
  });

  const [registerTable, { reload, multipleRemove, selected }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    showIndexColumn: false,
    api: (params) => {
      if (!currentInspectionItemId.value) return Promise.resolve({ data: { records: [] } });
      return inspectionPointList({
        ...params,
        id: currentInspectionItemId.value,
      });
    },
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 120,
      name: 'inspectionPoint',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerPointModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true, inspectionItemId: currentInspectionItemId.value });
  }

  function handleAdd() {
    if (!currentInspectionItemId.value) return;
    openModal(true, { update: false, inspectionItemId: currentInspectionItemId.value });
  }

  async function handleDelete(record: Recordable) {
    const { id: inspectionPointId } = record;
    await inspectionPointRemove([inspectionPointId]);
    await reload();
  }

  const [registerPointInfoModal, { openModal: openInfoModal }] = useModal();
  function handleInfo(record: Recordable) {
    const { id: inspectionPointId } = record;
    openInfoModal(true, inspectionPointId);
  }
</script>
