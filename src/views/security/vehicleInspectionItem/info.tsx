import { DescItem } from '@/components/Description';

import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';
import { Image } from 'ant-design-vue';

const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '检修项示意图',
    field: 'itemImg',
    render: (value) => {
      if (value) {
        return <Image src={value} alt="检修项示意图" height={200} />;
      }
      return '无';
    },
  },
  {
    label: '检修项编号',
    field: 'itemNum',
  },
  {
    label: '检修项名称',
    field: 'itemName',
  },
  {
    label: '顺序',
    field: 'orderNum',
  },
  {
    label: '状态',
    field: 'status',
    render: (value) => {
      return renderDict(value, DictEnum.VEHICLE_ITEM_STATUS);
    },
  },
  {
    label: '适用车型',
    field: 'vehicleType',
  },
  {
    label: '标准流程说明',
    field: 'standardProcess',
  },
  {
    label: '部件类型',
    field: 'componentType',
  },
  {
    label: '检查点总数',
    field: 'pointCount',
  },
  {
    label: '目标检测类别',
    field: 'detectionType',
    render: (value) => {
      return renderDict(value, DictEnum.VEHICLE_ITEM_TYPES);
    },
  },
  {
    label: '备注',
    field: 'remark',
    render: (value) => value || '无',
  },
  {
    label: '创建人',
    field: 'createUser',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '更新者',
    field: 'updateBy',
  },
  {
    label: '更新时间',
    field: 'updateTime',
  },
];
