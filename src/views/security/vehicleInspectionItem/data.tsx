import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
import { securityUploadApi } from '@/api/security/upload';
import { Image } from 'ant-design-vue';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  {
    title: '检修项示意图',
    dataIndex: 'itemImg',
    customRender({ value }) {
      if (value) {
        return <Image src={value} alt="检修项示意图" height={50} class="!object-contain" />;
      }
      return '无';
    },
  },
  { title: '检修项编号', dataIndex: 'itemNum' },
  { title: '检修项名称', dataIndex: 'itemName' },
  { title: '顺序', dataIndex: 'orderNum' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender({ value }) {
      return renderDict(value, DictEnum.VEHICLE_ITEM_STATUS);
    },
  },
  { title: '适用车型', dataIndex: 'vehicleType' },
  { title: '标准流程说明', dataIndex: 'standardProcess' },
  { title: '部件类型', dataIndex: 'componentType' },
  { title: '检查点总数', dataIndex: 'pointCount' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建人', dataIndex: 'createUser' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '检修项编号',
    field: 'itemNum',
    component: 'Input',
  },
  {
    label: '检修项名称',
    field: 'itemName',
    component: 'Input',
  },
  {
    label: '顺序',
    field: 'orderNum',
    component: 'InputNumber',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.VEHICLE_ITEM_STATUS),
    },
  },
  {
    label: '适用车型',
    field: 'vehicleType',
    component: 'Input',
  },
  {
    label: '标准流程说明',
    field: 'standardProcess',
    component: 'Input',
  },
  {
    label: '部件类型',
    field: 'componentType',
    component: 'Input',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    label:'主键',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '检修项示意图',
    field: 'itemImg',
    component: 'ImageUpload',
    componentProps: () => {
      return {
        api: securityUploadApi,
      };
    },
  },
  {
    label: '检修项编号',
    field: 'itemNum',
    component: 'Input',
    required: true,
  },
  {
    label: '检修项名称',
    field: 'itemName',
    component: 'Input',
    required: true,
  },
  {
    label: '顺序',
    field: 'orderNum',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.VEHICLE_ITEM_STATUS),
    },
    required: true,
  },
  {
    label: '适用车型',
    field: 'vehicleType',
    component: 'Input',
    required: true,
  },
  {
    label: '标准流程说明',
    field: 'standardProcess',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '部件类型',
    field: 'componentType',
    component: 'Input',
    required: true,
  },
  {
    label: '目标检测类别',
    field: 'detectionType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.VEHICLE_ITEM_TYPES),
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createDept',
    label: '创建部门',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createUser',
    label: '创建人',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: '更新者',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'Input',
    show: false,
  },
];
