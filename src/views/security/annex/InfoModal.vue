<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="附件信息详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { getAnnexInfo } from '@/api/security/annex';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'AnnexInfoInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(
    async (annexInfoId: string | number) => {
      showSkeleton.value = true;
      if (!annexInfoId) {
        return closeModal();
      }
      const response = await getAnnexInfo(annexInfoId);
      // 赋值
      setDescProps({ data: response });
      showSkeleton.value = false;
    },
  );

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '150px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
