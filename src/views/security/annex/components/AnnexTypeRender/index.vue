<template>
  <div class="flex items-center justify-center">
    <span v-if="checkImage" class="!h-12"> 图片 </span>

    <span v-else-if="checkVideo" class=""> 视频 </span>

    <span v-else-if="checkAudio">音频 </span>
  </div>
</template>
<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
  });

  const checkImage = computed(() =>
    ['.png', '.jpg', '.jpeg'].some((item) => props.value.includes(item)),
  );

  const checkVideo = computed(() => ['.mp4'].some((item) => props.value.includes(item)));

  const checkAudio = computed(() => ['.mp3'].some((item) => props.value.includes(item)));
</script>
<style lang=""></style>
