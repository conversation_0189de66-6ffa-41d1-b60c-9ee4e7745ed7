import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { taskTypeDict, taskTypeDictMap } from './dict';
import AnnexRender from './components/AnnexRender/index.vue';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '任务类型', dataIndex: 'taskType', customRender: ({ value }) => taskTypeDictMap[value] },
  {
    title: '附件地址',
    dataIndex: 'annexUrl',
    customRender: ({ value }) => <AnnexRender value={value || ''} />,
  },
  { title: '附件类型', dataIndex: 'annexType' },
  { title: '场景类型', dataIndex: 'sceneType' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建部门', dataIndex: 'createDept' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '更新者', dataIndex: 'updateBy' },
  { title: '更新时间', dataIndex: 'updateTime' },
  { title: '备注', dataIndex: 'remark' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '所属任务ID',
    field: 'taskId',
    component: 'Input',
  },
  {
    label: '任务类型',
    field: 'taskType',
    component: 'Select',
    componentProps: () => ({
      options: taskTypeDict,
    }),
  },
  {
    label: '附件类型',
    field: 'annexType',
    component: 'Input',
  },
  {
    label: '创建者',
    field: 'createBy',
    component: 'InputNumber',
  },
  {
    label: '创建部门',
    field: 'createDept',
    component: 'InputNumber',
  },
  {
    label: '场景id',
    field: 'sceneId',
    component: 'Input',
  },
  {
    label: '场景类型',
    field: 'sceneType',
    component: 'Input',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    label: '任务类型',
    field: 'taskType',
    component: 'Input',
    required: true,
  },
  {
    label: '附件地址',
    field: 'annexUrl',
    component: 'Input',
    required: true,
  },
  {
    label: '附件类型',
    field: 'annexType',
    component: 'Input',
    required: true,
  },
  {
    label: '创建者',
    field: 'createBy',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '创建部门',
    field: 'createDept',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '场景id',
    field: 'sceneId',
    component: 'Input',
    required: true,
  },
  {
    label: '场景类型',
    field: 'sceneType',
    component: 'Input',
    required: true,
  },
  {
    label: '所属任务ID',
    field: 'taskId',
    component: 'Input',
    required: true,
  },

  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: '更新者',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'Input',
    show: false,
  },
];
