import { DescItem } from '@/components/Description';

export const descSchema: DescItem[] = [
  {
    label: '附件类型',
    field: 'annexType',
  },
  {
    label: '附件地址',
    field: 'annexUrl',
  },
  {
    label: '创建者',
    field: 'createBy',
  },
  {
    label: '创建部门',
    field: 'createDept',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
  {
    label: '场景id',
    field: 'sceneId',
  },
  {
    label: '场景类型',
    field: 'sceneType',
  },
  {
    label: '所属任务ID',
    field: 'taskId',
  },
  {
    label: '任务类型',
    field: 'taskType',
  },
  {
    label: '更新者',
    field: 'updateBy',
  },
  {
    label: '更新时间',
    field: 'updateTime',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
];
