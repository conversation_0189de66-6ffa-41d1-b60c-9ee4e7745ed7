<template>
  <PageWrapper dense>
    <!-- 统计概览 -->
    <TaskStatsOverview ref="statsOverviewRef" :search-params="searchParams" />

    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <!-- <a-button @click="handleAdd">新增任务</a-button> -->
          <a-button
            type="primary"
            danger
            @click="multipleRemove(warehouseTaskRemove)"
            :disabled="!selected"
          >
            删除
          </a-button>
          <a-button
            @click="downloadExcel(warehouseTaskExport, '料库任务管理', getForm().getFieldsValue())"
          >
            导出
          </a-button>
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              // {
              //   label: '编辑',
              //   icon: IconEnum.EDIT,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleEdit.bind(null, record),
              // },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <WarehouseTaskModal @register="registerModal" @reload="reload" />
    <WarehouseTaskInfoModal @register="registerInfoModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    warehouseTaskList,
    warehouseTaskExport,
    warehouseTaskRemove,
  } from '@/api/security/warehouse';
  import WarehouseTaskModal from './Modal.vue';
  import WarehouseTaskInfoModal from './InfoModal.vue';
  import TaskStatsOverview from './TaskStatsOverview.vue';
  import { useModal } from '@/components/Modal';
  import { useDrawer } from '@/components/Drawer';
  import { downloadExcel } from '@/utils/file/download';
  import { warehouseTaskFormSchemas, warehouseTaskColumns } from './data';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'WarehouseTask' });

  const statsOverviewRef = ref();
  const searchParams = ref({});

  const [registerTable, { reload: tableReload, multipleRemove, selected, getForm }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    title: '料库任务管理',
    showIndexColumn: false,
    api: warehouseTaskList,
    rowKey: 'id',
    useSearchForm: true,
    beforeFetch: (params) => {
      // 更新查询参数，触发统计组件更新
      searchParams.value = { ...params };
      return params;
    },
    formConfig: {
      schemas: warehouseTaskFormSchemas,
      labelWidth: 120,
      name: 'warehouseTask',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
      // 日期选择格式化
      fieldMapToTime: [
        ['issueTime', ['params[issueTimeBegin]', 'params[issueTimeEnd]'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
        ['createTime', ['params[createTimeBegin]', 'params[createTimeEnd]'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ],
    },
    columns: warehouseTaskColumns,
    actionColumn: {
      width: 250,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();
  const [registerInfoModal, { openDrawer: openInfoDrawer }] = useDrawer();

  // 自定义 reload 函数，同时刷新表格和统计数据
  async function reload() {
    await tableReload();
    if (statsOverviewRef.value?.refresh) {
      await statsOverviewRef.value.refresh(searchParams.value);
    }
  }

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await warehouseTaskRemove([id]);
    await reload();
  }

  function handleInfo(record: Recordable) {
    const { id } = record;
    openInfoDrawer(true, id);
  }

</script>

<style scoped></style>
