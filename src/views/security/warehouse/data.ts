import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { stationOptionSelect } from '@/api/business/station';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
// import {
//   taskStatusDict,
//   taskStatusMap,
//   taskStatusColorMap,
//   utensilStatusDict,
//   utensilStatusMap,
//   utensilStatusColorMap,
// } from './dict';
// import { Tag } from 'ant-design-vue';
// import { h } from 'vue';

export const { renderDict } = useRender();

// ==================== 料库任务管理 ====================

/**
 * 料库任务表格列配置
 */
export const warehouseTaskColumns: BasicColumn[] = [
  { title: '任务编号', dataIndex: 'taskNum', width: 180 },
  { title: '施工日计划编号', dataIndex: 'constructionDayPlanNum', width: 150 },
  { title: '所属站点', dataIndex: 'stationName', width: 120 },
  {
    title: '任务状态',
    dataIndex: 'status',
    width: 100,
    customRender({ value }) {
      return renderDict(value, DictEnum.WAREHOUSE_TASK_STATUS);
    },
  },
  { title: '执行人', dataIndex: 'userName', width: 100 },
  { title: '工具种类', dataIndex: 'utensilTypeCount', width: 80 },
  { title: '工具数量', dataIndex: 'utensilCount', width: 80 },
  { title: '签发时间', dataIndex: 'issueTime', width: 150 },
  { title: '单位', dataIndex: 'company', width: 120 },
  { title: '工作票签发人', dataIndex: 'workTicketIssuer', width: 120 },
  { title: '工作领导人', dataIndex: 'workLeader', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 150 },
  { title: '创建时间', dataIndex: 'createTime', width: 150 },
];

/**
 * 料库任务搜索表单配置
 */
export const warehouseTaskFormSchemas: FormSchema[] = [
  {
    label: '任务编号',
    field: 'taskNum',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '所属站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
    colProps: { span: 6 },
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_TASK_STATUS),
    },
    colProps: { span: 6 },
  },
  {
    label: '执行人',
    field: 'userName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '单位',
    field: 'company',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '签发时间',
    field: 'issueTime',
    component: 'RangePicker',
    colProps: { span: 6 },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    colProps: { span: 6 },
  },
];

/**
 * 料库任务编辑表单配置
 */
export const warehouseTaskModalSchemas: FormSchema[] = [
  {
    label: '任务编号',
    field: 'taskNum',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '格式：LKRW-YYYYMMDDHHmm',
    },
  },
  {
    label: '施工日计划编号',
    field: 'constructionDayPlanNum',
    component: 'Input',
    required: true,
  },
  {
    label: '所属站点',
    field: 'stationId',
    slot: 'stationId',
    defaultValue: Number(localStorage.getItem('stationId')),
    required: true,
  },
  {
    label: '所属站点名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_TASK_STATUS),
    },
    required: true,
  },
  {
    label: '执行人',
    field: 'userId',
    slot: 'userId',
    required: true,
  },
  {
    label: '执行人名称',
    field: 'userName',
    component: 'Input',
    show: false,
  },
  {
    label: '签发时间',
    field: 'issueTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '审核工作票命令号',
    field: 'reviewWorkTicketCommandNum',
    component: 'Input',
  },
  {
    label: '准许作业命令号',
    field: 'permittedOperationCommandNum',
    component: 'Input',
  },
  {
    label: '单位',
    field: 'company',
    component: 'Input',
  },
  {
    label: '工作票签发人',
    field: 'workTicketIssuer',
    slot: 'workTicketIssuer',
  },
  {
    label: '审核人',
    field: 'reviewer',
    slot: 'reviewer',
  },
  {
    label: '工作领导人',
    field: 'workLeader',
    slot: 'workLeader',
  },
  {
    label: '驻站联络人',
    field: 'stationLiaisonPerson',
    slot: 'stationLiaisonPerson',
  },
  {
    label: '地线监护人',
    field: 'groundLineGuardian',
    slot: 'groundLineGuardian',
  },
  {
    label: '工作组员',
    field: 'workTeam',
    slot: 'workTeam',
  },
  {
    label: '停电范围',
    field: 'powerOutageRange',
    component: 'InputTextArea',
  },
  {
    label: '作业范围',
    field: 'scopeWork',
    component: 'InputTextArea',
  },
  {
    label: '工作任务',
    field: 'workTask',
    component: 'InputTextArea',
  },
  {
    label: '计划工作时间',
    field: 'plannedWorkingHours',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
