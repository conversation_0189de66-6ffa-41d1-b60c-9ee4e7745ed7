<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1200"
    title="任务详情"
    :footer="null"
    @register="registerInnerDrawer"
  >
    <div v-if="showSkeleton" class="skeleton-container">
      <Skeleton active :paragraph="{ rows: 8 }" />
    </div>
    <div v-else class="warehouse-task-info-container">
      <!-- 顶部信息区域 -->
      <div class="top-info-section">
        <!-- 任务基本信息 -->
        <div class="task-info-card">
          <div class="card-header">
            <div class="header-line"></div>
            <h3 class="card-title">任务基本信息</h3>
          </div>
          <div class="task-info-grid">
            <div class="info-item">
              <span class="info-label">任务编号</span>
              <span class="info-value">{{ taskData?.taskNum || 'W202505190001' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">施工计划编号</span>
              <span class="info-value">{{ taskData?.constructionDayPlanNum || 'W202505190001' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">站点名称</span>
              <span class="info-value">{{ taskData?.stationName || '测试站点1' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">任务状态</span>
              <a-tag :color="getTaskStatusColor(taskData?.status)" class="status-tag">
                {{ getTaskStatusText(taskData?.status) }}
              </a-tag>
            </div>
            <div class="info-item">
              <span class="info-label">执行人名称</span>
              <span class="info-value">{{ taskData?.userName || '张三' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工具种类</span>
              <span class="info-value">{{ toolTypeCount || 'XXXX' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工具数量</span>
              <span class="info-value">{{ totalToolCount || '4' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ formatDateTime(taskData?.createTime) || '2025-05-19 08:30' }}</span>
            </div>
          </div>
        </div>

        <!-- 出入库统计 -->
        <div class="statistics-card flex flex-col">
          <div class="card-header flex-none">
            <div class="header-line"></div>
            <h3 class="card-title">出入库统计</h3>
          </div>
          <div class="statistics-grid !flex-1 !h-0 !overflow-hidden">
            <div class="stat-item out-stat flex-1 w-0 h-full">
              <div class="stat-content">
                <div class="stat-label">领用数量</div>
                <div class="stat-value">{{ outCount || '85' }} <span class="stat-unit">个</span></div>
              </div>
              <div class="stat-visual">
                <img :src="warehouseInputImg" alt="领用" class="stat-image" />
              </div>
            </div>

            <div class="stat-item in-stat flex-1 w-0 h-full">
              <div class="stat-content">
                <div class="stat-label">归还数量</div>
                <div class="stat-value">{{ inCount || '50' }} <span class="stat-unit">个</span></div>
              </div>
              <div class="stat-visual">
                <img :src="warehouseOutputImg" alt="归还" class="stat-image" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具清单组件 -->
      <UtensilList
        :key="taskData?.id"
        :task-id="taskData?.id"
        :show-statistics="false"
        @statistics-change="handleStatisticsChange"
        ref="utensilListRef"
      />
    </div>


  </BasicDrawer>
</template>

<script setup lang="ts">
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { warehouseTaskInfo } from '@/api/security/warehouse';
  import { Skeleton, Tag as ATag } from 'ant-design-vue';
  import { ref } from 'vue';
  import { formatToDateTime } from '@/utils/dateUtil';
  import { taskStatusMap, taskStatusColorMap } from './dict';
  import UtensilList from './utensil/index.vue';

  // 图片资源
  import warehouseInputImg from '@/assets/images/warehouse/warehouse-input.png';
  import warehouseOutputImg from '@/assets/images/warehouse/warehouse-output.png';

  defineOptions({ name: 'WarehouseTaskInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const taskData = ref<Recordable>({});
  const utensilListRef = ref();

  // 统计数据（从子组件接收）
  const toolTypeCount = ref(0);
  const totalToolCount = ref(0);
  const outCount = ref(0);
  const inCount = ref(0);

  // 处理统计数据变化
  function handleStatisticsChange(statistics: any) {
    toolTypeCount.value = statistics.toolTypeCount;
    totalToolCount.value = statistics.totalToolCount;
    outCount.value = statistics.outCount;
    inCount.value = statistics.inCount;
  }



  const [registerInnerDrawer, { closeDrawer }] = useDrawerInner(async (taskId: string | number) => {
    showSkeleton.value = true;
    if (!taskId) {
      return closeDrawer();
    }

    try {
      const data = await warehouseTaskInfo(taskId);
      taskData.value = data;
      showSkeleton.value = false;
    } catch (error) {
      console.error('获取任务详情失败:', error);
      showSkeleton.value = false;
    }
  });

  // 状态相关方法
  function getTaskStatusColor(status: string) {
    return taskStatusColorMap[status] || 'default';
  }

  function getTaskStatusText(status: string) {
    return taskStatusMap[status] || status || '待领取';
  }

  // 时间格式化
  function formatDateTime(dateTime: string | Date) {
    if (!dateTime) return '';
    return formatToDateTime(dateTime);
  }
</script>

<style scoped>
  @media (max-width: 768px) {
    .task-info-grid {
      grid-template-columns: 1fr;
    }

    .statistics-grid {
      gap: 16px;
    }

    .stat-item {
      padding: 12px;
    }

    .stat-value {
      font-size: 20px;
    }

    .stat-image {
      width: 48px;
      height: 48px;
    }
  }

  .skeleton-container {
    padding: 24px;
  }

  .warehouse-task-info-container {
    padding: 0;
  }

  /* 顶部信息区域 */
  .top-info-section {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
  }

  /* 卡片通用样式 */
  .task-info-card,
  .statistics-card {
    overflow: hidden;
    border-radius: 12px;
    background: rgba(var(--color-primary-500), 0.04);
  }

  .task-info-card {
    flex: 1;
    width: 50%;
  }

  .statistics-card {
    flex: 1;
    width: 50%;
  }

  /* 卡片头部 */
  .card-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: #f5f7fa;
  }

  .header-line {
    width: 4px;
    height: 16px;
    margin-right: 12px;
    background: rgba(var(--color-primary-500), 1);
  }

  .card-title {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  /* 任务信息网格 */
  .task-info-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 16px 24px;
    padding: 20px;
  }

  .info-item {
    display: flex;
    flex-direction: row;
    gap: 4px;
  }

  .info-label {
    width: 120px;
    color: #737688;
    line-height: 1.5;
  }

  .info-value {
    color: #262626;
    font-weight: 500;
    line-height: 1.5;
  }

  .status-tag {
    align-self: flex-start;
    margin: 0;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  /* 统计区域 */
  .statistics-grid {
    display: flex;
    flex-direction: row;
    padding: 20px;
    gap: 24px;
  }

  .stat-item {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    padding: 24px 24px 0;
    border-radius: 8px;
    gap: 12px;
  }

  .out-stat {
    background-color: #FFF;
  }

  .in-stat {
    background-color: #FFF;
  }

  .stat-content {
    display: flex;
    flex-direction: column;
    align-items: start;
    width: 100%;
  }

  .stat-label {
    margin-bottom: 12px;
    color: #8c8c8c;
  }

  .stat-value {
    color: #262626;
    font-size: 36px;
    font-weight: 600;
    line-height: 1;
  }

  .stat-unit {
    margin-left: 4px;
    color: #8c8c8c;
    font-size: 14px;
    font-weight: 400;
  }

  /* 统计图片 */
  .stat-visual {
    flex-shrink: 0;
    margin-top: 42px;
  }

  .stat-image {
    width: 100%;
    object-fit: contain;
  }


</style>
