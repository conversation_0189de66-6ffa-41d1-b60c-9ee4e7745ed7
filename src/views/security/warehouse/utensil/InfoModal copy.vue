<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="工具详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <Skeleton v-if="showSkeleton" active />
    <Description
      v-else
      :schema="utensilListDescSchema"
      :data="modalData"
      :column="2"
    />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { warehouseTaskUtensilListInfo } from '@/api/security/warehouse';
  import { Description, useDescription } from '@/components/Description';
  import { utensilListDescSchema } from './info';

  console.log('Schema 配置:', utensilListDescSchema);
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'UtensilInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const modalData = ref<any>({});

  const [registerDescription, { setDescProps }] = useDescription({
    schema: utensilListDescSchema,
    column: 2,
    data: {},
  });

  const [registerInnerModal, { closeModal }] = useModalInner(async (utensilId: string | number) => {
    showSkeleton.value = true;
    if (!utensilId) {
      return closeModal();
    }

    try {
      const response = await warehouseTaskUtensilListInfo(utensilId);
      console.log('API 响应数据:', response);
      console.log('数据类型:', typeof response);
      console.log('数据键:', Object.keys(response || {}));
      console.log('utensilName 值:', response?.utensilName);

      // 赋值
      console.log('调用 setDescProps，传入数据:', { data: response });
      modalData.value = response;
      setDescProps({ data: response });
      console.log('setDescProps 调用完成');
      showSkeleton.value = false;
    } catch (error) {
      console.error('获取工具详情失败:', error);
      showSkeleton.value = false;
    }
  });
</script>

<style scoped></style>
