import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();


/**
 * 工具清单详情描述配置
 */
export const utensilListDescSchema: DescItem[] = [
  {
    field: 'utensilName',
    label: '工具名称',
  },
  {
    field: 'utensilType',
    label: '工具型号',
    render: (value) => value || '未设置',
  },
  {
    field: 'position',
    label: '工具位置',
  },
  {
    field: 'utensilCount',
    label: '工具数量',
  },
  {
    field: 'stockCount',
    label: '库存数量',
  },
  {
    field: 'receiveCount',
    label: '领取数量',
  },
  {
    field: 'returnCount',
    label: '归还数量',
  },
  {
    field: 'status',
    label: '状态',
    render: (value) => {
      if (!value) return '未设置';
      return renderDict(value, DictEnum.WAREHOUSE_UTENSIL_STATUS);
    },
  },
  {
    field: 'receiveTime',
    label: '领取时间',
    render: (value) => value || '未领取',
  },
  {
    field: 'returnTime',
    label: '归还时间',
    render: (value) => value || '未归还',
  },
  {
    field: 'remark',
    label: '备注',
    render: (value) => value || '无',
  },
];
