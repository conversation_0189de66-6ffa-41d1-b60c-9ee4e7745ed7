import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
// import {
//   taskStatusDict,
//   taskStatusMap,
//   taskStatusColorMap,
//   utensilStatusDict,
//   utensilStatusMap,
//   utensilStatusColorMap,
// } from './dict';
// import { Tag } from 'ant-design-vue';
// import { h } from 'vue';

export const { renderDict } = useRender();

/**
 * 工具清单表格列配置
 */
export const utensilListColumns: BasicColumn[] = [
  { title: '工具名称', dataIndex: 'utensilName', width: 150 },
  { title: '工具型号', dataIndex: 'utensilType', width: 120 },
  { title: '工具数量', dataIndex: 'utensilCount', width: 80 },
  { title: '领取数量', dataIndex: 'receiveCount', width: 80 },
  { title: '归还数量', dataIndex: 'returnCount', width: 80 },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender({ value }) {
      return renderDict(value, DictEnum.WAREHOUSE_UTENSIL_STATUS);
    },
  },
  { title: '领取时间', dataIndex: 'receiveTime', width: 150 },
  { title: '归还时间', dataIndex: 'returnTime', width: 150 },
  { title: '备注', dataIndex: 'remark', width: 150 },
];

/**
 * 优化后的工具清单表格列配置（用于UtensilListModal）
 */
export const optimizedUtensilListColumns: BasicColumn[] = [
  {
    title: '工具名称',
    dataIndex: 'utensilName',
    key: 'utensilName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '工具位置',
    dataIndex: 'utensilLocation',
    key: 'utensilLocation',
    width: 120,
    ellipsis: true,
  },
  {
    title: '料库单工具数量',
    dataIndex: 'warehouseCount',
    key: 'warehouseCount',
    width: 120,
    align: 'center',
  },
  {
    title: '领用数量',
    dataIndex: 'receiveCount',
    key: 'receiveCount',
    width: 100,
    align: 'center',
  },
  {
    title: '归还数量',
    dataIndex: 'returnCount',
    key: 'returnCount',
    width: 100,
    align: 'center',
  },
];

/**
 * 工具清单搜索表单配置
 */
export const utensilListFormSchemas: FormSchema[] = [
  {
    label: '工具名称',
    field: 'utensilName',
    component: 'Input',
    colProps: { span: 6 },
  },
  // {
  //   label: '工具型号',
  //   field: 'utensilType',
  //   component: 'Input',
  //   colProps: { span: 6 },
  // },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_UTENSIL_STATUS),
    },
    colProps: { span: 6 },
  },
];

/**
 * 工具清单编辑表单配置
 */
export const utensilListModalSchemas: FormSchema[] = [
  {
    label: '料库任务ID',
    field: 'taskId',
    component: 'Input',
    required: true,
    show: false,
  },
  {
    label: '工具ID',
    field: 'utensilId',
    component: 'Input',
    required: true,
  },
  {
    label: '工具名称',
    field: 'utensilName',
    component: 'Input',
    required: true,
  },
  {
    label: '工具型号',
    field: 'utensilType',
    component: 'Input',
  },
  {
    label: '工具数量',
    field: 'utensilCount',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
    },
  },
  {
    label: '领取数量',
    field: 'receiveCount',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
  },
  {
    label: '归还数量',
    field: 'returnCount',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WAREHOUSE_UTENSIL_STATUS),
    },
    required: true,
  },
  {
    label: '领取时间',
    field: 'receiveTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '归还时间',
    field: 'returnTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
];
