<template>
  <div class="utensil-list-container">
    <!-- 表格区域 -->
    <div class="table-section">
      <div class="table-header">
        <div class="header-line"></div>
        <h3 class="table-title">出入库工器具列表</h3>
      </div>

      <BasicTable 
        @register="registerTable" 
        class="utensil-table" 
        :style="{ '--vben-basic-table-form-container-padding': 0 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction
              stopButtonPropagation
              :actions="[
                {
                  label: '查看',
                  icon: IconEnum.PREVIEW,
                  type: 'primary',
                  ghost: true,
                  onClick: handleViewUtensil.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>

    <!-- 工具详情弹窗 -->
    <UtensilInfoModal @register="registerUtensilInfoModal" />
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { warehouseTaskUtensilListList } from '@/api/security/warehouse';
  import { ref, computed, watch, defineExpose } from 'vue';
  import { useModal } from '@/components/Modal';
  import { utensilListFormSchemas } from './data';
  import UtensilInfoModal from './InfoModal.vue';
  import { IconEnum } from '@/enums/appEnum';

  defineOptions({ name: 'UtensilList' });

  // Props 定义
  interface Props {
    taskId?: string | number;
    showStatistics?: boolean;
    height?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    showStatistics: true,
    height: 300,
  });

  // Emits 定义
  interface Emits {
    (e: 'statisticsChange', statistics: {
      toolTypeCount: number;
      totalToolCount: number;
      outCount: number;
      inCount: number;
    }): void;
  }

  const emit = defineEmits<Emits>();

  // 响应式数据
  const utensilList = ref<Recordable[]>([]);

  // 统计数据计算
  const toolTypeCount = computed(() => {
    return utensilList.value.length || 0;
  });

  const totalToolCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.utensilCount || 0), 0);
  });

  const outCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.receiveCount || 0), 0);
  });

  const inCount = computed(() => {
    return utensilList.value.reduce((total, item) => total + (item.returnCount || 0), 0);
  });

  // 监听统计数据变化，向父组件发送
  watch([toolTypeCount, totalToolCount, outCount, inCount], () => {
    emit('statisticsChange', {
      toolTypeCount: toolTypeCount.value,
      totalToolCount: totalToolCount.value,
      outCount: outCount.value,
      inCount: inCount.value,
    });
  });

  // 表格列配置
  const columns = [
    {
      title: '工具名称',
      dataIndex: 'utensilName',
      key: 'utensilName',
      width: 120,
    },
    {
      title: '工具型号',
      dataIndex: 'utensilType',
      key: 'utensilType',
      width: 100,
    },
    {
      title: '工具位置',
      dataIndex: 'position',
      key: 'position',
      width: 120,
    },
    {
      title: '料库单工具数量',
      dataIndex: 'utensilCount',
      key: 'utensilCount',
      width: 140,
      align: 'center' as const,
    },
    {
      title: '库存数量',
      dataIndex: 'stockCount',
      key: 'stockCount',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '领用数量',
      dataIndex: 'receiveCount',
      key: 'receiveCount',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '归还数量',
      dataIndex: 'returnCount',
      key: 'returnCount',
      width: 100,
      align: 'center' as const,
    },
  ];

  // 表格配置
  const [registerTable, { reload: reloadTable }] = useTable({
    inset: true,
    showIndexColumn: false,
    showTableSetting: false,
    scroll: {
      y: props.height,
    },
    api: async (params) => {
      // 确保 taskId 存在才调用接口
      if (!props.taskId) {
        return {
          rows: [],
          total: 0,
        };
      }

      try {
        const result = await warehouseTaskUtensilListList({
          ...params,
          taskId: props.taskId,
        });
        utensilList.value = result.rows || [];
        return result;
      } catch (error) {
        console.error('获取工器具列表失败:', error);
        utensilList.value = [];
        return {
          rows: [],
          total: 0,
        };
      }
    },
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: utensilListFormSchemas,
      labelWidth: 100,
      name: 'utensilListSearch',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 8,
      },
    },
    columns,
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    },
    actionColumn: {
      width: 250,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  // 工具详情弹窗
  const [registerUtensilInfoModal, { openModal: openUtensilInfoModal }] = useModal();

  // 工具操作方法
  function handleViewUtensil(record: Recordable) {
    openUtensilInfoModal(true, record.id);
  }

  // 重新加载数据
  function reload() {
    reloadTable();
  }

  // 获取统计数据
  function getStatistics() {
    return {
      toolTypeCount: toolTypeCount.value,
      totalToolCount: totalToolCount.value,
      outCount: outCount.value,
      inCount: inCount.value,
    };
  }

  // 监听 taskId 变化，重新加载数据
  watch(() => props.taskId, (newTaskId) => {
    if (newTaskId) {
      reload();
    }
  });

  // 暴露方法给父组件
  defineExpose({
    reload,
    getStatistics,
  });
</script>

<style scoped>


  /* 响应式设计 */
  @media (max-width: 768px) {
    .statistics-section {
      flex-direction: column;
      gap: 16px;
    }

    .stat-item {
      flex-direction: row;
      justify-content: space-between;
    }

    .stat-value {
      font-size: 20px;
    }
  }

  .utensil-list-container {
    width: 100%;
  }

  /* 统计信息区域 */
  .statistics-section {
    display: flex;
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 8px;
    background: rgba(var(--color-primary-500), 0.04);
    gap: 24px;
  }

  .stat-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
  }

  .stat-label {
    margin-bottom: 8px;
    color: #8c8c8c;
    font-size: 14px;
  }

  .stat-value {
    color: #262626;
    font-size: 24px;
    font-weight: 600;
  }

  /* 表格区域 */
  .table-section {
    overflow: hidden;
  }

  .table-header {
    display: flex;
    align-items: center;
    padding-top: 12px;
    padding-bottom: 24px;
  }

  .header-line {
    width: 4px;
    height: 16px;
    margin-right: 12px;
    background: rgba(var(--color-primary-500), 1);
  }

  .table-title {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
  }

  /* 表格内容样式 */
  .utensil-table :deep(.ant-table) {
    border-radius: 0;
  }

  .utensil-table :deep(.ant-table-thead > tr > th) {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #f5f7fa;
    color: #262626;
    font-weight: 600;
  }

  .utensil-table :deep(.ant-table-tbody > tr > td) {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .utensil-table :deep(.ant-table-tbody > tr:hover > td) {
    background: #f5f5f5;
  }

  .tool-name {
    color: #262626;
    font-weight: 500;
  }

  .tool-location {
    color: #8c8c8c;
    font-family: Monaco, Menlo, monospace;
  }

  .tool-type {
    color: #595959;
    font-size: 13px;
  }

  .count-value {
    color: #262626;
    font-weight: 500;
  }

  .return-count.not-returned {
    color: #ff4d4f;
  }

  .return-count.partial-returned {
    color: #faad14;
  }

  .return-count.fully-returned {
    color: #52c41a;
  }

  .view-btn {
    height: auto;
    padding: 0;
    color: #1890ff;
    font-size: 14px;
  }

  .view-btn:hover {
    color: #40a9ff;
  }
</style>
