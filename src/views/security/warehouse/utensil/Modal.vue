<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    :width="600"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { utensilListModalSchemas } from '../data';
  import { warehouseTaskUtensilListAdd, warehouseTaskUtensilListUpdate } from '@/api/security/warehouse';
  import { message } from 'ant-design-vue';
  import { ref, computed, unref } from 'vue';

  defineOptions({ name: 'UtensilModal' });

  const emit = defineEmits(['reload']);

  const isUpdate = ref<boolean>(false);
  const record = ref<Recordable>({});

  const title = computed(() => (unref(isUpdate) ? '编辑工具' : '新增工具'));

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: utensilListModalSchemas,
    showActionButtonGroup: false,
    baseColProps: {
      xs: 24,
      sm: 24,
      md: 24,
      lg: 24,
    },
  });

  const [registerInnerModal, { closeModal }] = useModalInner(async (data) => {
    await resetFields();
    isUpdate.value = !!data?.update;
    record.value = data?.record || {};

    if (unref(isUpdate)) {
      await setFieldsValue(data.record);
    } else {
      // 新增时设置默认值
      await setFieldsValue({
        status: '1', // 默认待领取状态
        utensilCount: 1,
        receiveCount: 0,
        returnCount: 0,
      });
    }
  });

  async function handleSubmit() {
    try {
      const values = await validate();
      
      if (unref(isUpdate)) {
        await warehouseTaskUtensilListUpdate({ ...record.value, ...values });
        message.success('编辑成功');
      } else {
        await warehouseTaskUtensilListAdd(values);
        message.success('新增成功');
      }
      
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('提交失败:', error);
    }
  }
</script>

<style scoped></style>
