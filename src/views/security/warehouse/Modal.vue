<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    :width="800"
  >
    <BasicForm @register="registerForm">
      <template #stationId="{ model, field }">
        <StationSelect v-model:value="model[field]" @change="handleStationChange" />
      </template>
      <template #userId="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('userName', value, worker)"
        />
      </template>
      <template #workTicketIssuer="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('workTicketIssuerName', value, worker)"
        />
      </template>
      <template #reviewer="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('reviewerName', value, worker)"
        />
      </template>
      <template #workLeader="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('workLeaderName', value, worker)"
        />
      </template>
      <template #stationLiaisonPerson="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('stationLiaisonPersonName', value, worker)"
        />
      </template>
      <template #groundLineGuardian="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('groundLineGuardianName', value, worker)"
        />
      </template>
      <template #workTeam="{ model, field }">
        <SimpleWorkerSelect
          v-model:value="model[field]"
          :stationId="model.stationId"
          @change="(value, worker) => handleWorkerChange('workTeamName', value, worker)"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { warehouseTaskModalSchemas } from './data';
  import { warehouseTaskAdd, warehouseTaskUpdate } from '@/api/security/warehouse';
  import { message } from 'ant-design-vue';
  import { ref, computed, unref } from 'vue';
  import StationSelect from '@/views/security/components/StationSelect/index.vue';
  import SimpleWorkerSelect from '@/views/security/components/WorkerSelect/SimpleWorkerSelect.vue';
  import { WorkerInfo } from '@/api/security/worker/model';

  defineOptions({ name: 'WarehouseTaskModal' });

  const emit = defineEmits(['reload']);

  const isUpdate = ref<boolean>(false);
  const record = ref<Recordable>({});

  const title = computed(() => (unref(isUpdate) ? '编辑料库任务' : '新增料库任务'));

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    schemas: warehouseTaskModalSchemas,
    showActionButtonGroup: false,
    baseColProps: {
      xs: 24,
      sm: 24,
      md: 12,
      lg: 12,
    },
  });

  const [registerInnerModal, { closeModal }] = useModalInner(async (data) => {
    await resetFields();
    isUpdate.value = !!data?.update;
    record.value = data?.record || {};

    if (unref(isUpdate)) {
      await setFieldsValue(data.record);
    } else {
      // 新增时设置默认值
      await setFieldsValue({
        status: '1', // 默认待领取状态
        issueTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      });
    }
  });

  async function handleSubmit() {
    try {
      const values = await validate();

      if (unref(isUpdate)) {
        await warehouseTaskUpdate({ ...record.value, ...values });
        message.success('编辑成功');
      } else {
        await warehouseTaskAdd(values);
        message.success('新增成功');
      }

      closeModal();
      emit('reload');
    } catch (error) {
      console.error('提交失败:', error);
    }
  }

  function handleStationChange(stationId: string, option: any) {
    setFieldsValue({
      stationId,
      stationName: option?.stationName || '',
    });
  }

  function handleWorkerChange(
    nameField: string,
    value: string | number | undefined,
    worker: WorkerInfo | null | undefined,
  ) {
    if (worker) {
      setFieldsValue({
        [nameField]: worker.userName,
      });
    } else {
      setFieldsValue({
        [nameField]: '',
      });
    }
  }
</script>

<style scoped></style>
