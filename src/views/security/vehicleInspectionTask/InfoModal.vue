<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="车辆检修任务信息"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />

    <TaskInspectionItemList
      :key="currentTaskId"
      v-if="currentTaskId"
      :params="{
        taskId: currentTaskId,
      }"
    />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { inspectionTaskInfo } from '@/api/security/vehicleInspectionTask';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';
  import TaskInspectionItemList from './TaskInspectionItemList/index.vue';

  defineOptions({ name: 'InspectionTaskInfoModal' });

  const currentTaskId = ref();

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(
    async (inspectionTaskId: string | number) => {
      showSkeleton.value = true;
      if (!inspectionTaskId) {
        return closeModal();
      }

      currentTaskId.value = inspectionTaskId;

      const response = await inspectionTaskInfo(inspectionTaskId);
      // 赋值
      setDescProps({ data: response });
      showSkeleton.value = false;
    },
  );

  const [registerDescription, { setDescProps }] = useDescription({
    column: 3,
    labelStyle: {
      width: '150px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
