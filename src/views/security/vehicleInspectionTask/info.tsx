import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();

// 左列字段配置 - 核心任务与车辆信息
export const leftColumnSchema: DescItem[] = [
  { label: '工作票号', field: 'workTicketNoOri' },
  { label: '作业车', field: 'workVehicle' },
  { label: '工区', field: 'workArea' },
  { label: '所属站点', field: 'stationName' },
  {
    label: '任务状态',
    field: 'status',
    render: (value) => {
      return renderDict(value, DictEnum.TASK_STATUS);
    },
  },
  { label: '开始时间', field: 'startTime' },
  { label: '结束时间', field: 'endTime' },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
];

// 右列字段配置 - 人员信息与进度统计
export const rightColumnSchema: DescItem[] = [
  { label: '正驾驶', field: 'workStDriver' },
  { label: '副驾驶', field: 'workCoDriver' },
  { label: '执行人', field: 'userId' },
  { label: '总检修项数', field: 'totalItems' },
  { label: '已完成项数', field: 'completedItems' },
  { label: '异常点总数', field: 'abnormalCount' },
  { label: '创建者', field: 'createBy' },
  { label: '创建时间', field: 'createTime' },
];

// 保持原有的 descSchema 以兼容现有代码
export const descSchema: DescItem[] = [...leftColumnSchema, ...rightColumnSchema];
