<template>
  <div class="task-stats-overview bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div
        v-for="(item, index) of statsModel"
        :key="index"
        class="stat-card bg-[rgba(0,52,119,0.04)] rounded-lg p-4 flex items-center space-x-2"
      >
        <img :src="item.icon" alt="" class="object-contain size-12" />

        <div class="flex-1 w-0">
          <span class="text-base text-gray-900">{{ item.label }}</span>
        </div>

        <div class="flex-none text-3xl font-bold text-primary-500">
          {{ item.value || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import { inspectionTaskCount } from '@/api/security/vehicleInspectionTask';
  import stats1 from '@/assets/images/vehicleInspectionTask/stats-1.png?url';
  import stats2 from '@/assets/images/vehicleInspectionTask/stats-3.png?url';
  import stats3 from '@/assets/images/vehicleInspectionTask/stats-4.png?url';

  defineOptions({ name: 'TaskStatsOverview' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const statsData = ref<{ status: number; num: number }[]>([]);

  const statsModel = computed(() => {
    // 计算总任务数
    const totalTasks = statsData.value.reduce((sum, item) => sum + item.num, 0);

    // 获取执行中任务数（status 为 1）
    const inProgressTasks = statsData.value.find((item) => item.status === 1)?.num || 0;

    // 获取已完成任务数（status 为 2）
    const completedTasks = statsData.value.find((item) => item.status === 2)?.num || 0;

    return [
      {
        label: '总任务数',
        value: totalTasks,
        icon: stats1,
      },
      {
        label: '执行中任务数',
        value: inProgressTasks,
        icon: stats2,
      },
      {
        label: '已完成任务数',
        value: completedTasks,
        icon: stats3,
      },
    ];
  });

  // 获取统计数据
  async function fetchStatsData(params?: any) {
    try {
      const response = await inspectionTaskCount(params);
      statsData.value = response || [];
    } catch (error) {
      console.error('获取统计数据失败:', error);
      statsData.value = [];
    }
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    (newParams) => {
      fetchStatsData(newParams);
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchStatsData(props.searchParams);
  });

  // 暴露刷新方法供父组件调用
  defineExpose({
    refresh: (params?: any) => fetchStatsData(params),
  });
</script>

<style scoped>
  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }
</style>
