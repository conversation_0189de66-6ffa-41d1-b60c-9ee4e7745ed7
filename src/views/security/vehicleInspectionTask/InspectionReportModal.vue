<template>
  <BasicModal v-bind="$attrs" title="检修报告" :footer="null" @register="registerInnerModal">
    <div v-show="!showSkeleton" class="inspection-report">
      <!-- 统计概览 -->
      <div class="report-header mb-6">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-2 xl:grid-cols-4 gap-4 mb-6">
          <div
            v-for="(item, index) of statsModel"
            :key="index"
            class="stat-card bg-[rgba(0,52,119,0.04)] rounded-lg p-4 flex items-center space-x-2"
          >
            <img :src="item.icon" alt="" class="object-contain size-12" />

            <div class="flex-1 w-0">
              <span class="text-base text-gray-900">{{ item.label }}</span>
            </div>

            <div class="flex-none text-3xl font-bold text-primary-500">
              {{ item.value || 0 }}
            </div>
          </div>
        </div>
      </div>

      <!-- 总体进度 -->
      <div class="progress-section mb-6">
        <h4 class="text-base font-medium mb-3 app-marker">总检查进度</h4>
        <div class="py-20 pb-8 px-8">
          <div class="bg-gray-200 rounded-full h-3 mb-2">
            <div
              class="bg-[#003477] h-full rounded-full transition-all duration-300 relative flex items-center justify-end"
              :style="{ width: `${reportData.overallProgress || 0}%` }"
            >
              <div
                class="text-white bg-primary-500 rounded-lg py-1 px-2 -mt-18 relative translate-x-1/2"
              >
                {{ reportData.overallProgress || 0 }}%
                <div
                  class="size-2 bg-primary-500 absolute bottom-0 translate-y-1/2 transform rotate-45 left-1/2 -translate-x-1/2"
                ></div>
              </div>
            </div>
          </div>
          <div class="flex justify-between text-sm text-gray-600 mt-4 px-24">
            <span
              >合格检查点
              <span class="text-green-500">{{ reportData.qualifiedItems || 0 }}</span></span
            >
            <span
              >不合格检查点
              <span class="text-red-500">{{ reportData.unqualifiedItems || 0 }}</span></span
            >
            <span
              >待确认检查点
              <span class="text-red-400">{{ reportData.pendingItems || 0 }}</span></span
            >
            <span
              >未检查点
              <span class="text-primary-500">{{ reportData.uncheckItems || 0 }}</span></span
            >
          </div>
        </div>
      </div>

      <!-- 检修工作详情 -->
      <div class="inspection-details">
        <h4 class="text-base font-medium mb-4 app-marker">检查项汇总</h4>

        <div class="space-y-4">
          <div
            v-for="(item, index) in reportData.inspectionItems"
            :key="index"
            class="inspection-item border border-gray-200 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <span
                  class="text-primary-500 bg-primary-50 h-5 min-w-5 text-xs flex items-center justify-center font-medium mr-2 rounded-full"
                  >{{ index + 1 }}
                </span>

                <div class="font-medium">{{ item.itemName }}</div>
              </div>

              <div class="flex items-center">
                <span class="text-sm text-gray-500 mr-2">完成时间: {{ item.completedTime }}</span>
              </div>
            </div>

            <div class="grid grid-cols-3 gap-4 mb-3">
              <div class="text-sm">
                <span class="text-gray-600">检查点总数:</span>
                <span class="font-medium ml-1">{{ item.totalChecks }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">不合格数量:</span>
                <span class="font-medium ml-1 text-red-600">{{ item.unqualifiedChecks }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">检察员:</span>
                <span class="font-medium ml-1">{{ item.totalChecks }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">合格数量:</span>
                <span class="font-medium ml-1">{{ item.totalChecks }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">检查结果:</span>
                <span class="ml-1 px-2 py-1 rounded text-xs" :class="getStatusClass(item.status)">
                  {{ getStatusText(item.status) }}
                </span>
              </div>
              <div class="text-sm text-gray-600">
                <span class="font-medium">处理意见:</span>
                <span class="ml-1">{{ item.processingOpinion }}</span>
              </div>
            </div>

            <!-- 异常信息 -->
            <div v-if="item.hasAbnormal" class="bg-red-50 border border-red-200 rounded p-3">
              <div class="flex items-center mb-2">
                <Icon icon="mdi:alert" class="text-red-500 mr-2" />
                <span class="text-red-700 font-medium">异常项信息</span>
              </div>
              <p class="text-sm text-red-600">{{ item.abnormalInfo }}</p>
              <div class="flex items-center mt-2 text-xs text-red-500">
                <Icon icon="mdi:camera" class="mr-1" />
                <span>已拍照记录，需要进一步检查</span>
              </div>

              <div v-if="item.grabberUrl" class="mt-3 flex items-center">
                <div class="mr-2">
                  <Image
                    v-for="item_1 of item.grabberUrl.split(',')"
                    :key="item_1"
                    :src="item_1"
                    class="!size-24"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 12 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Skeleton, Image } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { ref, computed } from 'vue';
  import { inspectionTaskReport } from '@/api/security/vehicleInspectionTask';
  import type { VehicleTaskItemReportVo } from '@/api/security/vehicleInspectionTask/model';
  import report1 from '@/assets/images/vehicle/report-1.png?url';
  import report2 from '@/assets/images/vehicle/report-2.png?url';
  import report3 from '@/assets/images/vehicle/report-3.png?url';
  import report4 from '@/assets/images/vehicle/report-4.png?url';

  defineOptions({ name: 'InspectionReportModal' });

  const showSkeleton = ref<boolean>(true);
  const reportData = ref<any>({});

  const statsModel = computed(() => {
    const value = [
      {
        label: '检查项总数',
        value: reportData.value.totalItems,
        icon: report1,
      },
      {
        label: '合格检查项',
        value: reportData.value.qualifiedItems,
        icon: report2,
      },
      {
        label: '需整改检查项',
        value: reportData.value.improvementItems,
        icon: report3,
      },
      {
        label: '总检查点',
        value: reportData.value.totalPoints,
        icon: report4,
      },
    ];

    return value;
  });

  const [registerInnerModal, { closeModal }] = useModalInner(async (taskId: string | number) => {
    showSkeleton.value = true;
    if (!taskId) {
      return closeModal();
    }

    // 获取检修报告数据
    await getInspectionReport(taskId);
    showSkeleton.value = false;
  });

  // 获取检修报告数据的函数
  async function getInspectionReport(taskId: string | number) {
    try {
      const apiData = await inspectionTaskReport(taskId);
      // 将接口数据映射到组件期望的数据结构
      reportData.value = {
        taskId: apiData.taskId,
        totalItems: apiData.totalItems,
        qualifiedItems: apiData.passPoints, // 合格检查项使用合格点数
        improvementItems: apiData.unPassPoints, // 需整改检查项使用不合格点数
        totalPoints: apiData.totalPoints,
        overallProgress: parseFloat(apiData.completionRate) || 0, // 转换完成率为数字
        qualifiedPoints: apiData.passPoints,
        unqualifiedItems: apiData.unPassPoints,
        pendingItems: apiData.doPassPoints,
        uncheckItems:
          apiData.totalPoints - apiData.passPoints - apiData.unPassPoints - apiData.doPassPoints,
        inspectionItems: apiData.vehicleTaskItemReportVos.map((item: VehicleTaskItemReportVo) => ({
          ...item,
          grabberUrl: item.grabberUrl,
          itemName: item.itemName,
          completedTime: item.createTime, // 接口未提供，使用当前时间
          inspector: item.userName,
          totalChecks: item.totalPoints,
          unqualifiedChecks: item.unPassPoints,
          status: getStatusFromCheckResult(item.checkResult),
          processingOpinion: getProcessingOpinion(item.checkResult, item.checkRemark),
          hasAbnormal: item.unPassPoints > 0,
          abnormalInfo:
            item.unPassPoints > 0
              ? `发现 ${item.unPassPoints} 个不合格检查点，需要进一步处理`
              : undefined,
        })),
      };
    } catch (error) {
      console.error('获取检修报告数据失败:', error);
      // 发生错误时使用默认数据
      reportData.value = {
        taskId,
        totalItems: 0,
        qualifiedItems: 0,
        improvementItems: 0,
        totalPoints: 0,
        overallProgress: 0,
        qualifiedPoints: 0,
        unqualifiedItems: 0,
        pendingItems: 0,
        uncheckItems: 0,
        inspectionItems: [],
      };
    }
  }

  // 根据检查结果转换状态
  function getStatusFromCheckResult(checkResult: number): string {
    switch (checkResult) {
      case 0:
        return 'pending';
      case 1:
        return 'qualified';
      case 2:
        return 'unqualified';
      default:
        return 'pending';
    }
  }

  // 根据检查结果和备注生成处理意见
  function getProcessingOpinion(checkResult: number, _checkRemark?: string): string {
    if (_checkRemark) {
      return _checkRemark;
    }

    if (checkResult === 1) {
      return '无异常';
    } else if (checkResult === 2) {
      return '发现异常，需要进一步检查和处理';
    } else if (checkResult === 0) {
      return '待确认检查结果';
    }

    return '待确认检查结果';
  }

  function getStatusClass(status: string) {
    switch (status) {
      case 'qualified':
        return 'bg-green-100 text-green-800';
      case 'unqualified':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  function getStatusText(status: string) {
    switch (status) {
      case 'qualified':
        return '合格';
      case 'unqualified':
        return '需整改';
      case 'pending':
        return '待检查';
      default:
        return '未知';
    }
  }
</script>

<style scoped>
  .inspection-report {
    max-height: 70vh;
    overflow-y: auto;
  }

  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }

  .inspection-item {
    transition: all 0.2s ease;
  }

  .inspection-item:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }
</style>
