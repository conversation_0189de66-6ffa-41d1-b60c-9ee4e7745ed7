import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
import { stationOptionSelect } from '@/api/business/station';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '车牌号', dataIndex: 'licensePlate' },
  { title: '车辆型号', dataIndex: 'vehicleModel' },
  { title: '工作票号', dataIndex: 'workTicketNoOri' },
  { title: '所属站点', dataIndex: 'stationName' },
  { title: '执行人', dataIndex: 'executorName' },
  {
    title: '检修项',
    dataIndex: 'inspectionProgress',
    customRender({ record }) {
      const { totalItems = 0, completedItems = 0 } = record;
      return `${totalItems}/${completedItems}`;
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender({ value }) {
      return renderDict(value, DictEnum.TASK_STATUS);
    },
  },
  { title: '异常点总数', dataIndex: 'abnormalCount' },
  { title: '开始时间', dataIndex: 'startTime' },
  { title: '结束时间', dataIndex: 'endTime' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '车牌号',
    field: 'licensePlate',
    component: 'Input',
  },
  {
    label: '车辆型号',
    field: 'vehicleModel',
    component: 'Input',
  },
  {
    label: '工作票号',
    field: 'workTicketNoOri',
    component: 'Input',
  },
  {
    label: '所属站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
  },
  {
    label: '执行人',
    field: 'executorName',
    component: 'Input',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TASK_STATUS),
    },
  },
  {
    label: '开始时间',
    field: 'startTime',
    component: 'RangePicker',
  },
  {
    label: '结束时间',
    field: 'endTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键',
    component: 'Input',
    show: false,
  },
  {
    label: '车牌号',
    field: 'licensePlate',
    component: 'Input',
    required: true,
  },
  {
    label: '车辆型号',
    field: 'vehicleModel',
    component: 'Input',
    required: true,
  },
  {
    label: '工作票号',
    field: 'workTicketNoOri',
    component: 'Input',
    required: true,
  },
  {
    label: '所属站点',
    field: 'stationId',
    slot: 'stationId',
    defaultValue: Number(localStorage.getItem('stationId')),
    required: true,
  },
  {
    label: '所属站点名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '执行人',
    field: 'userId',
    component: 'Input',
    required: true,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '进行中', value: '进行中' },
        { label: '已完成', value: '已完成' },
      ],
    },
    required: true,
  },

  {
    field: 'startTime',
    label: '开始时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'endTime',
    label: '结束时间',
    component: 'Input',
    show: false,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
