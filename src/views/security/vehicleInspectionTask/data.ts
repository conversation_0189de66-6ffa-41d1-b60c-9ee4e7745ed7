import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '工作票号', dataIndex: 'workTicketNoOri', align: 'left' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender({ value }) {
      return renderDict(value, DictEnum.TASK_STATUS);
    },
  },
  { title: '工区', dataIndex: 'workArea' },
  { title: '作业车', dataIndex: 'workVehicle' },
  { title: '正驾驶', dataIndex: 'workStDriver' },
  { title: '副驾驶', dataIndex: 'workCoDriver' },
  { title: '开始时间', dataIndex: 'startTime' },
  { title: '结束时间', dataIndex: 'endTime' },
  { title: '异常点总数', dataIndex: 'abnormalCount' },
  { title: '已完成项数', dataIndex: 'completedItems' },
  { title: '总检修项数', dataIndex: 'totalItems' },

  { title: '备注', dataIndex: 'remark' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '工作票号',
    field: 'workTicketNoOri',
    component: 'Input',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TASK_STATUS),
    },
  },
  {
    label: '工区',
    field: 'workArea',
    component: 'Input',
  },
  {
    label: '作业车',
    field: 'workVehicle',
    component: 'Input',
  },
  {
    label: '正驾驶',
    field: 'workStDriver',
    component: 'Input',
  },
  {
    label: '副驾驶',
    field: 'workCoDriver',
    component: 'Input',
  },
  {
    label: '开始时间',
    field: 'startTime',
    component: 'RangePicker',
  },
  {
    label: '结束时间',
    field: 'endTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键',
    component: 'Input',
    show: false,
  },
  {
    label: '工作票号',
    field: 'workTicketNoOri',
    component: 'Input',
    required: true,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TASK_STATUS),
    },
    required: true,
  },
  {
    label: '工区',
    field: 'workArea',
    component: 'Input',
    required: true,
  },
  {
    label: '作业车',
    field: 'workVehicle',
    component: 'Input',
    required: true,
  },
  {
    label: '正驾驶',
    field: 'workStDriver',
    component: 'Input',
    required: true,
  },
  {
    label: '副驾驶',
    field: 'workCoDriver',
    component: 'Input',
  },
  // {
  //   label: '开始时间',
  //   field: 'startTime',
  //   component: 'DatePicker',
  //   componentProps: {
  //     showTime: true,
  //     format: 'YYYY-MM-DD HH:mm:ss',
  //   },
  // },
  // {
  //   label: '结束时间',
  //   field: 'endTime',
  //   component: 'DatePicker',
  //   componentProps: {
  //     showTime: true,
  //     format: 'YYYY-MM-DD HH:mm:ss',
  //   },
  // },
  // {
  //   label: '异常点总数',
  //   field: 'abnormalCount',
  //   component: 'InputNumber',
  //   componentProps: {
  //     min: 0,
  //   },
  // },
  // {
  //   label: '已完成项数',
  //   field: 'completedItems',
  //   component: 'InputNumber',
  //   componentProps: {
  //     min: 0,
  //   },
  // },
  // {
  //   label: '总检修项数',
  //   field: 'totalItems',
  //   component: 'InputNumber',
  //   componentProps: {
  //     min: 0,
  //   },
  // },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
