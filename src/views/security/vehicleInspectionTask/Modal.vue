<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    @cancel="resetForm"
  >
    <BasicForm @register="registerForm">
      <template #stationId="{ model, field }">
        <StationSelect v-model:value="model[field]" v-model:label="model.stationName" />
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { computed, ref } from 'vue';
  import {
    inspectionTaskUpdate,
    inspectionTaskAdd,
    inspectionTaskInfo,
  } from '@/api/security/vehicleInspectionTask';
  import { modalSchemas } from './data';
  import StationSelect from '@/views/security/components/StationSelect/index.vue';

  defineOptions({ name: 'InspectionTask' });

  const emit = defineEmits(['register', 'reload']);

  const isUpdate = ref<boolean>(false);

  const title = computed<string>(() => {
    return isUpdate.value ? '编辑车辆检修任务' : '新增车辆检修任务';
  });

  const retData = ref<any>({});

  const [registerInnerModal, { modalLoading, closeModal }] = useModalInner(
    async (data: { record?: Recordable; update: boolean }) => {
      modalLoading(true);
      const { record, update } = data;
      isUpdate.value = update;
      if (update && record) {
        const ret = await inspectionTaskInfo(record.id);
        retData.value = ret;

        await setFieldsValue(ret);
      }
      modalLoading(false);
    },
  );

  const [registerForm, { setFieldsValue, resetForm, validate }] = useForm({
    baseColProps: {
      span: 24,
    },
    labelWidth: 100,
    name: 'inspectionTask_modal',
    showActionButtonGroup: false,
    schemas: modalSchemas,
  });

  async function handleSubmit() {
    try {
      modalLoading(true);
      const data = await validate();
      if (isUpdate.value) {
        await inspectionTaskUpdate(data);
      } else {
        await inspectionTaskAdd(data);
      }
      emit('reload');
      closeModal();
      await resetForm();
    } catch (e) {
      console.log(e);
    } finally {
      modalLoading(false);
    }
  }
</script>

<style scoped></style>
