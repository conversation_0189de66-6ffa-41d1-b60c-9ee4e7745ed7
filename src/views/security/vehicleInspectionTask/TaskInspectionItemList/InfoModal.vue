<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="任务检查项详情信息"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { itemRecordInfo } from '@/api/security/vehicleInspectionTaskItem/index';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'ItemRecordInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(async (record) => {
    showSkeleton.value = true;
    if (!record) {
      return closeModal();
    }
    // 赋值
    setDescProps({ data: record });
    showSkeleton.value = false;
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '150px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
