import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
// import { Image } from 'ant-design-vue';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '检修项编号', dataIndex: 'itemNum' },
  { title: '检修项名称', dataIndex: 'itemName', width: 160, align: 'left' },
  { title: '顺序', dataIndex: 'orderNum' },
  {
    title: '是否完成',
    dataIndex: 'isCompleted',
    customRender({ value }) {
      return renderDict(value, DictEnum.VEHICLE_ITEM_COMPLETED);
    },
  },
  {
    title: '目标检测类别',
    dataIndex: 'detectionType',
    customRender({ value }) {
      return renderDict(value, DictEnum.VEHICLE_ITEM_TYPES);
    },
  },
  {
    title: '本项下总检修点',
    dataIndex: 'pointProgress',
    customRender({ record }) {
      const { pointTotal = 0, pointCompleted = 0 } = record;
      return `${pointTotal}/${pointCompleted}`;
    },
  },
  { title: '适用车型', dataIndex: 'vehicleType' },
  { title: '标准流程说明', dataIndex: 'standardProcess', width: 300, align: 'left' },
  { title: '部件类型', dataIndex: 'componentType' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '检修项编号',
    field: 'itemNum',
    component: 'Input',
  },
  {
    label: '检修项名称',
    field: 'itemName',
    component: 'Input',
  },
  {
    label: '是否完成',
    field: 'isCompleted',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.VEHICLE_ITEM_COMPLETED),
    },
  },
  // {
  //   label: '目标检测类别',
  //   field: 'detectionType',
  //   component: 'Select',
  //   componentProps: {
  //     options: getDictOptions(DictEnum.VEHICLE_ITEM_TYPES),
  //   },
  // },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键',
    component: 'Input',
    show: false,
  },
  {
    label: '关联检修项ID',
    field: 'itemId',
    component: 'Input',
    required: true,
  },
  {
    label: '检修项名称',
    field: 'itemName',
    component: 'Input',
  },
  {
    label: '检修项编号',
    field: 'itemNum',
    component: 'Input',
    required: true,
  },
  {
    label: '关联任务ID',
    field: 'taskId',
    component: 'Input',
    required: true,
  },
  {
    label: '检测状态', // 修改标签与表格列名一致
    field: 'isCompleted',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 }, // 调整选项顺序与表格显示一致
        { label: '是', value: 1 },
        { label: '异常', value: 2 }, // 新增"异常"选项
      ],
    },
    required: true,
  },
  {
    label: '本项下总检修点数',
    field: 'pointTotal',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '本项下已完成点数',
    field: 'pointCompleted',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '本项下异常点数',
    field: 'pointAbnormal',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createDept',
    label: '创建部门',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: '更新者',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'Input',
    show: false,
  },
];
