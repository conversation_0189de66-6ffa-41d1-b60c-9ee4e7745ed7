<template>
  <div class="-ml-4">
    <div class="flex items-center" v-for="(item, index) of urlList" :key="index">
      <a-button type="link" @click="onLinkClick(item)" block>{{ item }}</a-button>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import { Button as AButton } from 'ant-design-vue';

  const props = defineProps({
    urls: {
      type: String,
      default: '',
    },
  });

  const urlList = computed(() => (props.urls ? props.urls.split(',') : []));

  function onLinkClick(url) {
    window.open(url);
  }
</script>

<style></style>
