<template>
  <BasicDrawer
    v-bind="$attrs"
    :width="1200"
    title="车辆检修任务信息"
    :show-footer="false"
    @register="registerInnerDrawer"
    @close="handleClose"
  >
    <div class="inspection-task-info" v-if="!showSkeleton">
      <!-- 任务基本信息 -->
      <div class="info-section">
        <div class="section-title app-marker"> 任务基本信息 </div>
        <div class="info-grid">
          <div class="info-column">
            <div v-for="item in leftColumnSchema" :key="item.field" class="info-item">
              <span class="info-label">{{ item.label }}</span>
              <span class="info-value">
                <template v-if="item.render">
                  <component :is="item.render(taskData[item.field], taskData)" />
                </template>
                <template v-else>
                  {{ taskData[item.field] || '-' }}
                </template>
              </span>
            </div>
          </div>
          <div class="info-column">
            <div v-for="item in rightColumnSchema" :key="item.field" class="info-item">
              <span class="info-label">{{ item.label }}</span>
              <span class="info-value">
                <template v-if="item.render">
                  <component :is="item.render(taskData[item.field], taskData)" />
                </template>
                <template v-else>
                  {{ taskData[item.field] || '-' }}
                </template>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计模块和视频 -->
      <div class="stats-video-section">
        <!-- 统计模块 -->
        <div class="stats-module">
          <div class="section-title app-marker"> 统计模块 </div>
          <div class="stats-content">
            <div class="progress-chart px-16">
              <div class="chart-container">
                <div ref="chartRef" class="echarts-container"></div>
              </div>
              <div class="stats-legend">
                <div class="legend-item">
                  <span class="legend-dot completed"></span>
                  <span class="legend-text">已完成检修数量</span>
                  <span class="legend-value">{{ completedItems }}</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot abnormal"></span>
                  <span class="legend-text">异常检修点</span>
                  <span class="legend-value">{{ abnormalCount }}</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot remaining"></span>
                  <span class="legend-text">未完成检修</span>
                  <span class="legend-value">{{ remainingItems }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 视频模块 -->
        <div class="video-module flex flex-col">
          <div class="section-title app-marker flex-none"> 视频 </div>
          <div class="video-content flex-1 h-0">
            <div class="video-buttons flex h-full">
              <a-button
                type="default"
                class="video-btn flex-1 w-0 !h-full !border-1 !border-transparent !hover:border-primary-200 flex justify-between !rounded-xl"
                @click="handleLiveStream(taskData)"
              >
                <span class="text-black text-base font-bold">视频直播</span>
                <img :src="playIcon" alt="play" class="play-icon" />
              </a-button>

              <a-button
                type="default"
                class="video-btn flex-1 w-0 !h-full !border-1 !border-transparent !hover:border-primary-200 flex justify-between !rounded-xl"
                @click="handleRecord(taskData)"
              >
                <span class="text-black text-base font-bold">视频录播</span>
                <img :src="playIcon" alt="play" class="play-icon" />
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 车辆检修任务信息列表 -->
      <div class="table-section">
        <div class="section-title app-marker"> 车辆检修任务信息列表 </div>
        <TaskInspectionItemList
          :key="currentTaskId"
          v-if="currentTaskId"
          :params="{
            taskId: currentTaskId,
          }"
          class="-mx-6"
        />
      </div>
    </div>

    <!-- 骨架屏 -->
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 12 }" />
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { inspectionTaskInfo } from '@/api/security/vehicleInspectionTask';
  import { leftColumnSchema, rightColumnSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref, computed, watch, nextTick } from 'vue';
  import TaskInspectionItemList from './TaskInspectionItemList/index.vue';
  import playIcon from '@/assets/images/vehicleInspectionTask/info-play.png';
  import { useECharts } from '@/hooks/web/useECharts';
  import type { EChartsOption } from 'echarts';

  defineOptions({ name: 'InspectionTaskInfoDrawer' });

  defineProps({
    handleLiveStream: {
      type: Function,
      default: () => false,
    },
    handleRecord: {
      type: Function,
      default: () => false,
    },
  });

  const currentTaskId = ref();
  const taskData = ref<any>({});
  const showSkeleton = ref<boolean>(true);
  const chartRef = ref<HTMLDivElement>();

  // 初始化 ECharts
  const { setOptions, initCharts } = useECharts(chartRef as any);

  const [registerInnerDrawer, { closeDrawer }] = useDrawerInner(
    async (inspectionTaskId: string | number) => {
      showSkeleton.value = true;
      if (!inspectionTaskId) {
        return closeDrawer();
      }

      currentTaskId.value = inspectionTaskId;

      try {
        const response = await inspectionTaskInfo(inspectionTaskId);
        taskData.value = response;
        showSkeleton.value = false;
      } catch (error) {
        console.error('获取任务信息失败:', error);
        showSkeleton.value = false;
      }
    },
  );

  // 计算统计数据
  const totalItems = computed(() => taskData.value.totalItems || 0);
  const completedItems = computed(() => taskData.value.completedItems || 0);
  const abnormalCount = computed(() => taskData.value.abnormalCount || 0);
  const remainingItems = computed(
    () => totalItems.value - completedItems.value - abnormalCount.value,
  );

  // ECharts 配置
  const chartOptions = computed<EChartsOption>(() => ({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      show: false, // 隐藏图例，使用自定义图例
    },
    series: [
      {
        name: '检修统计',
        type: 'pie',
        radius: ['60%', '90%'], // 环形图
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 12,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: completedItems.value,
            name: '已完成检修',
            itemStyle: {
              color: '#003477',
            },
          },
          {
            value: abnormalCount.value,
            name: '异常检修点',
            itemStyle: {
              color: '#CE2121',
            },
          },
          {
            value: remainingItems.value,
            name: '未完成检修',
            itemStyle: {
              color: '#d9d9d9',
            },
          },
        ],
      },
    ],
    graphic: [
      {
        type: 'text',
        left: 'center',
        top: '35%',
        style: {
          text: '检修项数量',
          fontSize: 10,
          fill: '#666',
        },
      },
      {
        type: 'text',
        left: 'center',
        top: '50%',
        style: {
          text: totalItems.value.toString(),
          fontSize: 16,
          fontWeight: 'bold',
          fill: '#333',
        },
      },
    ],
  }));

  // 监听数据变化，更新图表
  watch(
    [totalItems, completedItems, abnormalCount],
    () => {
      nextTick(() => {
        initCharts();
        setOptions(chartOptions.value);
      });
    },
    { immediate: true },
  );

  const handleClose = () => {
    closeDrawer();
  };
</script>

<style scoped lang="scss">
  .inspection-task-info {
    .info-section {
      margin-bottom: 24px;
      padding: 16px;
      border-radius: 8px;
      background: rgba(var(--color-primary-500), 0.04);
      // box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

      .section-title {
        @apply text-gray-900;

        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        // border-bottom: 2px solid #f0f0f0;
        font-size: 16px;
        font-weight: 600;

        .title-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;

        .info-column {
          .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;

            .info-label {
              flex-shrink: 0;
              width: 120px;
              color: #666;
              font-weight: 500;
            }

            .info-value {
              flex: 1;
              color: #333;
              word-break: break-all;
            }
          }
        }
      }
    }

    .stats-video-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      margin-bottom: 24px;

      .stats-module,
      .video-module {
        padding: 16px;
        border-radius: 8px;
        background: rgba(var(--color-primary-500), 0.04);
        // box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

        .section-title {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 8px;
          // border-bottom: 2px solid #f0f0f0;
          color: black;
          font-size: 16px;
          font-weight: 600;

          .title-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }
      }

      .stats-content {
        .progress-chart {
          display: flex;
          align-items: center;
          gap: 24px;

          .chart-container {
            position: relative;

            .echarts-container {
              width: 120px;
              height: 120px;
            }
          }

          .stats-legend {
            flex: 1;

            .legend-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;

              .legend-dot {
                width: 12px;
                height: 12px;
                margin-right: 8px;
                border-radius: 50%;

                &.completed {
                  background-color: #003477;
                }

                &.abnormal {
                  background-color: #ce2121;
                }

                &.remaining {
                  background-color: #d9d9d9;
                }
              }

              .legend-text {
                flex: 1;
                color: #666;
              }

              .legend-value {
                color: #333;
                font-weight: 600;
              }
            }
          }
        }
      }

      .video-content {
        .video-buttons {
          display: flex;
          gap: 16px;

          .video-btn {
            display: flex;
            align-items: center;
            height: 40px;
            padding: 0 16px;

            .play-icon {
              width: 48px;
              height: 48px;
              margin-right: 8px;
            }
          }
        }
      }
    }

    .table-section {
      // border-radius: 8px;
      // padding: 16px;
      // background: rgba(var(--color-primary-500), 0.04);
      // box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        // border-bottom: 2px solid #f0f0f0;
        color: black;
        font-size: 16px;
        font-weight: 600;

        .title-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }
    }
  }
</style>
