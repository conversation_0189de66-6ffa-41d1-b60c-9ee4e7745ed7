import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { stationOptionSelect } from '@/api/business/station';
// import { DictEnum } from '@/enums/dictEnum';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
import { Image } from 'ant-design-vue';
import { securityUploadApi } from '@/api/security/upload';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  {
    title: '人脸照片',
    dataIndex: 'image',
    customRender({ value }) {
      if (value) {
        return <Image src={value} alt="人脸照片" height={50} class="!object-contain" />;
      }

      return '无';
    },
  },
  { title: '姓名', dataIndex: 'userName' },
  { title: '工号', dataIndex: 'workerNum' },
  {
    title: '性别',
    dataIndex: 'gender',
    customRender({ value }) {
      return renderDict(value, DictEnum.SYS_USER_SEX);
    },
  },
  { title: '联系方式', dataIndex: 'phone' },
  { title: '所属站点', dataIndex: 'stationName' },
  {
    title: '人员类型',
    dataIndex: 'type',
    customRender({ value }) {
      return renderDict(value, DictEnum.PERSONNEL_TYPE);
    },
  },
  {
    title: '资质信息',
    dataIndex: 'qualificationInformation',
    customRender({ value }) {
      return renderDict(value, DictEnum.QUALIFICATION_INFORMATION);
    },
  },
  { title: '累计违规次数', dataIndex: 'violationCount' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '姓名',
    field: 'userName',
    component: 'Input',
  },
  {
    label: '工号',
    field: 'workerNum',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'gender',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_USER_SEX),
    },
  },
  {
    label: '联系方式',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '所属站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
  },
  {
    label: '人员类型',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.PERSONNEL_TYPE),
    },
  },
  {
    label: '资质信息',
    field: 'qualificationInformation',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.QUALIFICATION_INFORMATION),
    },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键ID',
    component: 'Input',
    show: false,
  },
  {
    label: '人脸照片',
    field: 'image',
    component: 'ImageUpload',
    componentProps: () => {
      return {
        api: securityUploadApi,
      };
    },
  },
  {
    label: '姓名',
    field: 'userName',
    component: 'Input',
    required: true,
  },
  {
    label: '工号',
    field: 'workerNum',
    component: 'Input',
    required: true,
  },
  {
    label: '性别',
    field: 'gender',
    required: true,
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.SYS_USER_SEX),
    },
  },
  {
    label: '联系方式',
    field: 'phone',
    component: 'Input',
    required: true,
  },
  {
    label: '所属站点',
    field: 'stationId',
    slot: 'stationId',
    defaultValue: Number(localStorage.getItem('stationId')),
    required: true,
  },
  {
    label: '所属站点名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '人员类型',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.PERSONNEL_TYPE),
    },
    required: true,
  },
  {
    label: '资质信息',
    field: 'qualificationInformation',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.QUALIFICATION_INFORMATION),
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'createDept',
    label: '创建部门',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: '更新者',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'Input',
    show: false,
  },
];
