import { DescItem } from '@/components/Description';
import { Image } from 'ant-design-vue';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '人脸照片',
    field: 'image',
    render: (value) => {
      if (value) {
        return <Image src={value} alt="人脸照片" height={150} />;
      }
      return '无';
    },
  },
  {
    label: '姓名',
    field: 'userName',
  },
  {
    label: '工号',
    field: 'workerNum',
  },
  {
    label: '性别',
    field: 'gender',
    render: (value) => {
      return renderDict(value, DictEnum.SYS_USER_SEX);
    },
  },
  {
    label: '联系方式',
    field: 'phone',
  },
  {
    label: '所属站点',
    field: 'stationName',
  },
  {
    label: '人员类型',
    field: 'type',
    render: (value) => {
      return renderDict(value, DictEnum.PERSONNEL_TYPE);
    },
  },
  {
    label: '资质信息',
    field: 'qualificationInformation',
    render: (value) => {
      return renderDict(value, DictEnum.QUALIFICATION_INFORMATION);
    },
  },
  {
    label: '累计违规次数',
    field: 'violationCount',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建者',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];
