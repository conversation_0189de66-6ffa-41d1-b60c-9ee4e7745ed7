<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="人员信息详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { workerInfoDetail } from '@/api/security/worker';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'WorkerInfoInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(
    async (workerInfoId: string | number) => {
      showSkeleton.value = true;
      if (!workerInfoId) {
        return closeModal();
      }
      const response = await workerInfoDetail(workerInfoId);
      // 赋值
      setDescProps({ data: response });
      showSkeleton.value = false;
    },
  );

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '150px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
