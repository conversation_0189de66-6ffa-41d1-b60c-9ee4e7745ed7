<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    @cancel="resetForm"
  >
    <BasicForm @register="registerForm">
      <template #preStepIdList="{ model, field }">
        <StepBeforeSelect
          v-model:value="model[field]"
          :params="{
            stepId: model.id,
          }"
        />
      </template>

      <template #annexType="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="图片" value="1" />
          <SelectOption label="语音" value="2" />
          <SelectOption label="视频" value="3" />
          <SelectOption label="无附件" value="99" />
        </Select>
      </template>
      <template #isKeyPoint="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="是" value="1" />
          <SelectOption label="否" value="0" />
        </Select>
      </template>
      <template #isMultipleAttachments="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="是" value="1" />
          <SelectOption label="否" value="2" />
        </Select>
      </template>
      <template #roleType="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="负责人" value="leader" />
          <SelectOption label="联系人" value="contact" />
          <SelectOption label="监护人" value="guardian" />
        </Select>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { computed, ref, unref } from 'vue';
  import {
    workflowStepUpdate,
    workflowStepAdd,
    workflowStepInfo,
  } from '@/api/security/railwayWorkflowStep';
  import { modalSchemas } from './data';
  import { Select } from 'ant-design-vue';
  import StepBeforeSelect from './components/StepBeforeSelect/index.vue';

  const SelectOption = Select.Option;

  defineOptions({ name: 'WorkflowStep' });

  const emit = defineEmits(['register', 'reload']);

  const isUpdate = ref<boolean>(false);

  const title = computed<string>(() => {
    return isUpdate.value ? '编辑流程环节' : '新增流程环节';
  });

  const retData = ref<any>({});

  const [registerInnerModal, { modalLoading, closeModal }] = useModalInner(
    async (data: { record?: Recordable; update: boolean }) => {
      modalLoading(true);
      const { record, update } = data;
      isUpdate.value = update;
      if (update && record) {
        const ret = await workflowStepInfo(record.id);
        retData.value = ret;

        await setFieldsValue(ret);
      }
      modalLoading(false);
    },
  );

  const [registerForm, { setFieldsValue, resetForm, validate, updateSchema }] = useForm({
    baseColProps: {
      span: 24,
    },
    labelWidth: 150,
    name: 'workflowStep_modal',
    showActionButtonGroup: false,
    schemas: modalSchemas,
  });

  async function handleSubmit() {
    try {
      modalLoading(true);
      const data = await validate();
      if (unref(isUpdate)) {
        await workflowStepUpdate(data);
      } else {
        await workflowStepAdd(data);
      }
      emit('reload');
      closeModal();
      await resetForm();
    } catch (e) {
      console.log(e);
    } finally {
      modalLoading(false);
    }
  }
</script>

<style scoped></style>
