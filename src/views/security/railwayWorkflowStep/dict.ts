export const roleTypeDict = [
  { label: '工作领导人', value: 'leader' },
  { label: '驻站联络人', value: 'contact' },
  { label: '地线监护人', value: 'guardian' },
];

export const roleTypeMap = Object.fromEntries(roleTypeDict.map((item) => [item.value, item.label]));

export const isKeyPointDict = [
  { label: '是', value: '1' },
  { label: '否', value: '0' },
];

export const isKeyPointMap = Object.fromEntries(
  isKeyPointDict.map((item) => [item.value, item.label]),
);

export const annexTypeDict = [
  { label: '图片', value: '1' },
  { label: '语音', value: '2' },
  { label: '视频', value: '3' },
  { label: '无附件', value: '99' },
];

export const annexTypeMap = Object.fromEntries(
  annexTypeDict.map((item) => [item.value, item.label]),
);
