<script setup lang="ts">
  import { Select, SelectOption } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import { workflowStepPreStageStepListById } from '@/api/security/railwayWorkflowStep';

  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  });

  const stationList = ref<any[]>([]);

  watch(
    () => props.params?.stepId,
    async (stepId) => {
      stationList.value = await workflowStepPreStageStepListById({
        stepId,
      });
    },
    {
      immediate: true,
    },
  );

  const selectRef = ref();
</script>
<template>
  <Select
    ref="selectRef"
    v-bind="$attrs"
    optionFilterProp="title"
    placeholder="请选择"
    optionLabelProp="title"
    showArrow
    mode="multiple"
    :allowClear="true"
  >
    <SelectOption
      v-for="item in stationList"
      :key="item.id"
      :value="item.id"
      :title="item.stepName"
    >
      <div class="flex flex-row gap-1.5">
        <span>{{ item.stepName }}</span>
      </div>
    </SelectOption>
  </Select>
</template>
