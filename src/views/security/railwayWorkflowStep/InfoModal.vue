<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="流程环节信息"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { workflowStepInfo } from '@/api/security/railwayWorkflowStep';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'WorkflowStepInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(
    async (workflowStepId: string | number) => {
      showSkeleton.value = true;
      if (!workflowStepId) {
        return closeModal();
      }
      const response = await workflowStepInfo(workflowStepId);
      // 赋值
      setDescProps({ data: response });
      showSkeleton.value = false;
    },
  );

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '180px',
      minWidth: '180px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
