import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '设备标识',
    field: 'deviceNo',
  },
  {
    label: '型号',
    field: 'model',
  },
  {
    label: '出厂编号',
    field: 'factoryNo',
  },
  {
    label: '所属站点',
    field: 'stationName',
  },
  {
    label: '状态',
    field: 'status',
    render: (value) => {
      return renderDict(value, DictEnum.AR_DEVICE_STATUS);
    },
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建者',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];
