<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="AR眼镜设备详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { glassesInfo } from '@/api/security/glasses';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'GlassDeviceInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(async (glassesId: string | number) => {
    showSkeleton.value = true;
    if (!glassesId) {
      return closeModal();
    }
    const response = await glassesInfo(glassesId);
    // 赋值
    setDescProps({ data: response });
    showSkeleton.value = false;
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '150px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
