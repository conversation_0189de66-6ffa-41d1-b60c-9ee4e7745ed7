import { DictEnum } from '@/enums/dictEnum';
import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { stationOptionSelect } from '@/api/business/station';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '设备标识', dataIndex: 'deviceNo' },
  { title: '型号', dataIndex: 'model' },
  { title: '出厂编号', dataIndex: 'factoryNo' },
  { title: '所属站点', dataIndex: 'stationName' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender({ value }) {
      return renderDict(value, DictEnum.AR_DEVICE_STATUS);
    },
  },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建者', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '设备标识',
    field: 'deviceNo',
    component: 'Input',
  },
  {
    label: '型号',
    field: 'model',
    component: 'Input',
  },
  {
    label: '出厂编号',
    field: 'factoryNo',
    component: 'Input',
  },
  {
    label: '所属站点',
    field: 'stationId',
    component: 'ApiSelect',
    componentProps: {
      api: stationOptionSelect,
      labelField: 'stationName',
      valueField: 'stationId',
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.AR_DEVICE_STATUS),
    },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    label:'主键',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '设备标识',
    field: 'deviceNo',
    component: 'Input',
    required: true,
  },
  {
    label: '型号',
    field: 'model',
    component: 'Input',
    required: true,
  },
  {
    label: '出厂编号',
    field: 'factoryNo',
    component: 'Input',
    required: true,
  },
  {
    label: '所属站点',
    field: 'stationId',
    slot: 'stationId',
    defaultValue: Number(localStorage.getItem('stationId')),
    required: true,
  },
  {
    label: '所属站点名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.AR_DEVICE_STATUS),
    },
    required: true,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
