<template>
  <div class="worker-select">
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :allowClear="allowClear"
      :disabled="disabled"
      :mode="mode"
      show-search
      :filter-option="false"
      :not-found-content="loading ? undefined : null"
      @search="handleSearch"
      @change="handleChange"
      @clear="handleClear"
      @focus="onFocus"
      :style="{ width: '100%' }"
    >
      <template #notFoundContent>
        <a-spin v-if="loading" size="small" />
        <span v-else>暂无数据</span>
      </template>
      <a-select-option
        v-for="worker in workerOptions"
        :key="worker.id"
        :value="worker.id"
        :label="worker.userName"
      >
        <div class="worker-option">
          <div class="worker-name">{{ worker.userName }}</div>
          <div class="worker-info">
            <span class="worker-num">工号: {{ worker.workerNum }}</span>
            <span class="worker-station">{{ worker.stationName }}</span>
          </div>
        </div>
      </a-select-option>
    </a-select>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import { Select as ASelect, SelectOption as ASelectOption, Spin as ASpin } from 'ant-design-vue';
  import { workerInfoList } from '@/api/security/worker';
  import { WorkerInfo } from '@/api/security/worker/model';
  import { useDebounceFn } from '@vueuse/core';

  interface Props {
    value?: string | number | string[] | number[];
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
    mode?: 'multiple' | 'tags';
    stationId?: string | number; // 可选的站点ID过滤
  }

  interface Emits {
    (e: 'update:value', value: string | number | string[] | number[] | undefined): void;
    (e: 'change', value: string | number | string[] | number[] | undefined, option: any): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择人员',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const selectedValue = ref(props.value);
  const workerOptions = ref<WorkerInfo[]>([]);
  const loading = ref(false);
  const searchKeyword = ref('');

  // 监听外部value变化
  watch(
    () => props.value,
    (newValue) => {
      selectedValue.value = newValue;
    },
    { immediate: true },
  );

  // 防抖搜索
  const debouncedSearch = useDebounceFn(async (keyword: string) => {
    await fetchWorkers(keyword);
  }, 300);

  // 获取人员列表
  async function fetchWorkers(keyword = '') {
    try {
      loading.value = true;
      const params: any = {
        pageNum: 1,
        pageSize: 50, // 限制返回数量
      };

      // 添加搜索条件
      if (keyword) {
        params.userName = keyword;
        params.workerNum = keyword;
      }

      // 添加站点过滤
      if (props.stationId) {
        params.stationId = props.stationId;
      }

      const response = await workerInfoList(params);
      workerOptions.value = (response as any).rows || [];
    } catch (error) {
      console.error('获取人员列表失败:', error);
      workerOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 搜索处理
  function handleSearch(value: string) {
    searchKeyword.value = value;
    debouncedSearch(value);
  }

  // 选择变化处理
  function handleChange(value: string | number | string[] | number[], option: any) {
    selectedValue.value = value;
    emit('update:value', value);
    emit('change', value, option);
  }

  // 清空处理
  function handleClear() {
    selectedValue.value = undefined;
    emit('update:value', undefined);
    emit('change', undefined, null);
  }

  // 根据ID获取人员信息
  function getWorkerById(id: string | number): WorkerInfo | undefined {
    return workerOptions.value.find((worker) => worker.id === Number(id));
  }

  // 根据ID获取人员姓名
  function getWorkerNameById(id: string | number): string {
    const worker = getWorkerById(id);
    return worker?.userName || '';
  }

  // 监听站点ID变化，重新加载数据
  watch(
    () => props.stationId,
    () => {
      fetchWorkers(searchKeyword.value);
    },
  );

  // 组件激活时加载初始数据
  function onFocus() {
    fetchWorkers();
  }

  // 暴露方法给父组件
  defineExpose({
    getWorkerById,
    getWorkerNameById,
    refresh: () => fetchWorkers(searchKeyword.value),
  });
</script>

<style scoped>
  .worker-select {
    width: 100%;
  }

  .worker-option {
    padding: 4px 0;
  }

  .worker-name {
    margin-bottom: 2px;
    color: #333;
    font-weight: 500;
  }

  .worker-info {
    display: flex;
    color: #666;
    font-size: 12px;
    gap: 12px;
  }

  .worker-num {
    color: #1890ff;
  }

  .worker-station {
    color: #52c41a;
  }
</style>
