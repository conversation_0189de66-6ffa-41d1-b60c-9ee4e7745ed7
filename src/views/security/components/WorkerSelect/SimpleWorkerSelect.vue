<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :allowClear="allowClear"
    :disabled="disabled"
    show-search
    :not-found-content="loading ? undefined : null"
    @search="handleSearch"
    @change="handleChange"
    @clear="handleClear"
    @focus="onFocus"
    :style="{ width: '100%' }"
  >
    <template #notFoundContent>
      <a-spin v-if="loading" size="small" />
      <span v-else>暂无数据</span>
    </template>
    <a-select-option
      v-for="worker in workerOptions"
      :key="worker.id"
      :value="worker.id"
      :label="worker.userName"
    >
      <div class="flex justify-between items-center">
        <span class="font-medium">{{ worker.userName }}</span>
        <span class="text-xs text-gray-500">{{ worker.workerNum }}</span>
      </div>
    </a-select-option>
  </a-select>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted } from 'vue';
  import { Select as ASelect, SelectOption as ASelectOption, Spin as ASpin } from 'ant-design-vue';
  import { workerInfoList } from '@/api/security/worker';
  import { WorkerInfo } from '@/api/security/worker/model';
  import { useDebounceFn } from '@vueuse/core';

  interface Props {
    value?: string | number;
    placeholder?: string;
    allowClear?: boolean;
    disabled?: boolean;
    stationId?: string | number;
  }

  interface Emits {
    (e: 'update:value', value: string | number | undefined): void;
    (e: 'change', value: string | number | undefined, worker: WorkerInfo | null | undefined): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: '请选择人员',
    allowClear: true,
    disabled: false,
  });

  const emit = defineEmits<Emits>();

  const selectedValue = ref(props.value);
  const workerOptions = ref<WorkerInfo[]>([]);
  const loading = ref(false);
  const searchKeyword = ref('');

  // 监听外部value变化
  watch(
    () => props.value,
    (newValue) => {
      selectedValue.value = newValue;
    },
    { immediate: true },
  );

  // 防抖搜索
  const debouncedSearch = useDebounceFn(async (keyword: string) => {
    await fetchWorkers(keyword);
  }, 300);

  // 获取人员列表
  async function fetchWorkers(keyword = '') {
    try {
      loading.value = true;
      const params: any = {
        pageNum: 1,
        pageSize: 50,
      };

      if (keyword) {
        params.userName = keyword;
      }

      if (props.stationId) {
        params.stationId = props.stationId;
      }

      const response = await workerInfoList(params);
      workerOptions.value = (response as any).rows || [];
    } catch (error) {
      console.error('获取人员列表失败:', error);
      workerOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  function handleSearch(value: string) {
    searchKeyword.value = value;
    debouncedSearch(value);
  }

  function handleChange(value: string | number | undefined) {
    selectedValue.value = value;
    const worker = value ? workerOptions.value.find((w) => w.id === Number(value)) : null;
    emit('update:value', value);
    emit('change', value, worker);
  }

  function handleClear() {
    selectedValue.value = undefined;
    emit('update:value', undefined);
    emit('change', undefined, null);
  }

  watch(
    () => props.stationId,
    () => {
      fetchWorkers(searchKeyword.value);
    },
  );

  function onFocus() {
    fetchWorkers();
  }

  // 暴露获取人员信息的方法
  defineExpose({
    getSelectedWorker: () => {
      return selectedValue.value
        ? workerOptions.value.find((w) => w.id === Number(selectedValue.value))
        : null;
    },
    refresh: () => fetchWorkers(searchKeyword.value),
  });
</script>

<style scoped></style>
