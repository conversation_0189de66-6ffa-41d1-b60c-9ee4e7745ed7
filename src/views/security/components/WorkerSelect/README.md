# 人员选择组件使用说明

## 组件概述

本目录包含两个人员选择组件：

1. **WorkerSelect** (`index.vue`) - 完整功能的人员选择组件，支持单选和多选
2. **SimpleWorkerSelect** (`SimpleWorkerSelect.vue`) - 简化版人员选择组件，专用于表单单选场景

## 组件特性

### 共同特性
- ✅ 基于 worker 接口的人员数据
- ✅ 支持搜索功能（按姓名搜索）
- ✅ 支持按站点过滤
- ✅ 防抖搜索，提升性能
- ✅ 加载状态提示
- ✅ 清空功能

### WorkerSelect 特有功能
- 支持多选模式
- 显示详细的人员信息（工号、站点）
- 更丰富的展示样式

### SimpleWorkerSelect 特有功能
- 专为表单设计
- 简洁的显示样式
- 更轻量的实现

## 使用方法

### 1. 基础用法

```vue
<template>
  <!-- 简单选择 -->
  <SimpleWorkerSelect 
    v-model:value="selectedWorkerId" 
    placeholder="请选择人员"
  />
  
  <!-- 完整功能选择 -->
  <WorkerSelect 
    v-model:value="selectedWorkerIds" 
    :mode="multiple"
    placeholder="请选择人员"
  />
</template>

<script setup>
import WorkerSelect from '@/views/security/components/WorkerSelect/index.vue';
import SimpleWorkerSelect from '@/views/security/components/WorkerSelect/SimpleWorkerSelect.vue';

const selectedWorkerId = ref();
const selectedWorkerIds = ref([]);
</script>
```

### 2. 在表单中使用

```vue
<template>
  <BasicForm @register="registerForm">
    <template #userId="{ model, field }">
      <SimpleWorkerSelect 
        v-model:value="model[field]" 
        :stationId="model.stationId"
        @change="handleWorkerChange"
      />
    </template>
  </BasicForm>
</template>

<script setup>
function handleWorkerChange(value, worker) {
  if (worker) {
    // 自动填充人员姓名
    setFieldsValue({
      userName: worker.userName,
    });
  }
}
</script>
```

### 3. 按站点过滤

```vue
<template>
  <SimpleWorkerSelect 
    v-model:value="workerId" 
    :stationId="currentStationId"
  />
</template>
```

## API 参数

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | string \| number \| string[] \| number[] | - | 选中的值 |
| placeholder | string | '请选择人员' | 占位符文本 |
| allowClear | boolean | true | 是否允许清空 |
| disabled | boolean | false | 是否禁用 |
| mode | 'multiple' \| 'tags' | - | 选择模式（仅 WorkerSelect） |
| stationId | string \| number | - | 站点ID过滤 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | value | 值变化时触发 |
| change | (value, worker) | 选择变化时触发，返回值和人员对象 |

### 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getSelectedWorker | - | WorkerInfo \| null | 获取当前选中的人员信息 |
| refresh | - | - | 刷新人员列表 |

## 在料库任务中的应用

在料库任务管理中，以下字段已配置为使用人员选择组件：

- **执行人** (`userId`) - 对应 `userName`
- **工作票签发人** (`workTicketIssuer`) - 对应 `workTicketIssuerName`
- **审核人** (`reviewer`) - 对应 `reviewerName`
- **工作领导人** (`workLeader`) - 对应 `workLeaderName`
- **驻站联络人** (`stationLiaisonPerson`) - 对应 `stationLiaisonPersonName`
- **地线监护人** (`groundLineGuardian`) - 对应 `groundLineGuardianName`
- **工作组员** (`workTeam`) - 对应 `workTeamName`

## 数据流程

1. 用户选择人员时，组件会：
   - 保存人员ID到对应字段
   - 自动填充人员姓名到对应的姓名字段
   - 触发 change 事件，传递人员完整信息

2. 表单提交时：
   - 人员ID字段用于后端关联
   - 人员姓名字段用于显示

## 注意事项

1. **站点过滤**：当传入 `stationId` 时，只会显示该站点的人员
2. **搜索功能**：支持按人员姓名进行模糊搜索
3. **性能优化**：使用防抖搜索，避免频繁请求
4. **错误处理**：网络错误时会显示空列表，不会影响表单使用

## 扩展建议

1. **缓存优化**：可以添加人员数据缓存，减少重复请求
2. **权限控制**：可以根据用户权限过滤可选人员
3. **批量操作**：可以添加批量选择功能
4. **历史记录**：可以记录最近选择的人员，提升用户体验
