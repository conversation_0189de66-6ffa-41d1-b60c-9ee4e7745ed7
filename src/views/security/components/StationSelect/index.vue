<script setup lang="ts">
  import { Station } from '@/api/business/station/model';
  import { Select, SelectOption } from 'ant-design-vue';
  import { onMounted, ref } from 'vue';
  import { stationOptionSelect } from '@/api/business/station';

  const props = defineProps({
    value: {
      type: [Number, String],
      default: void 0,
    },
  });

  const emit = defineEmits(['update:value', 'update:label']);

  const stationList = ref<Station[]>([]);

  onMounted(async () => {
    stationList.value = await stationOptionSelect();
    if (props.value) {
      const label = stationList.value.find((item) => item.stationId == props.value)?.stationName;
      if (label) emit('update:label', label);
    }
  });

  const selectRef = ref();

  function onChange(value, item) {
    emit('update:label', item.title);
  }
</script>
<template>
  <Select
    ref="selectRef"
    :value="$props.value"
    v-bind="$attrs"
    optionFilterProp="title"
    placeholder="请选择"
    optionLabelProp="title"
    :allowClear="true"
    @change="onChange"
  >
    <SelectOption
      v-for="item in stationList"
      :key="item.stationId"
      :value="item.stationId"
      :title="item.stationName"
    >
      <div class="flex flex-row gap-1.5">
        <span>{{ item.stationName }}</span>
      </div>
    </SelectOption>
  </Select>
</template>
