import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';

export const columns: BasicColumn[] = [
  // { title: '主键ID', dataIndex: 'id' },
  // { title: 'AR名称', dataIndex: 'arName' },
  { title: '任务名称', dataIndex: 'taskName' },
  { title: '文件名称', dataIndex: 'fileName' },
  { title: '原始文件名称', dataIndex: 'fileNameOrig' },
  { title: '文件后缀', dataIndex: 'fileSuffix' },
  { title: '文件地址', dataIndex: 'fileUrl' },
];

export const formSchemas: FormSchema[] = [
  {
    label: 'AR名称',
    field: 'arName',
    component: 'Input',
  },
  // {
  //   label: '任务名称',
  //   field: 'taskName',
  //   component: 'Input',
  // },
  {
    label: '文件名称',
    field: 'fileName',
    component: 'Input',
  },
  {
    label: '原始文件名称',
    field: 'fileNameOrig',
    component: 'Input',
  },
  // {
  //   label: '文件后缀',
  //   field: 'fileSuffix',
  //   component: 'Input',
  // },
  // {
  //   label: '文件地址',
  //   field: 'fileUrl',
  //   component: 'Input',
  // },
];

export const modalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '主键ID',
    component: 'Input',
    show: false,
  },
  {
    label: 'AR名称',
    field: 'arName',
    component: 'Input',
    required: true,
  },
  {
    label: '任务名称',
    field: 'taskName',
    component: 'Input',
    required: true,
  },
  {
    label: '文件名称',
    field: 'fileName',
    component: 'Input',
    required: true,
  },
  {
    label: '原始文件名称',
    field: 'fileNameOrig',
    component: 'Input',
    required: true,
  },
  {
    label: '文件后缀',
    field: 'fileSuffix',
    component: 'Input',
    required: true,
  },
  {
    label: '文件地址',
    field: 'fileUrl',
    component: 'Input',
    required: true,
  },
];
