<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="录播信息详情"
    :footer="null"
    @register="registerInnerModal"
  >
    <video
      v-if="activeRecord && getOpen"
      :src="activeRecord.fileUrl"
      autoplay
      controls
      class="object-cover w-full min-h-96"
    ></video>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { ref } from 'vue';

  defineOptions({ name: 'RecordedBroadcastPreviewModal' });

  const activeRecord = ref<Recordable>();

  const [registerInnerModal, { closeModal, getOpen }] = useModalInner(
    async (record: Recordable) => {
      if (!record) {
        return closeModal();
      }

      activeRecord.value = record;
    },
  );
</script>

<style scoped></style>
