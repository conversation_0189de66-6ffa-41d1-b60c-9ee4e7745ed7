<template>
  <PageWrapper dense>
    <BasicTable
      @register="registerTable"
      :style="{ '--vben-basic-table-form-container-padding': 0 }"
    >
      <!-- <template #toolbar>
        <Space>
          <a-button
            class="<sm:hidden"
            @click="
              downloadExcel(recordedBroadcastExport, '录播信息管理', getForm().getFieldsValue())
            "
            >导出</a-button
          >
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(recordedBroadcastRemove)"
            :disabled="!selected"
            >删除</a-button
          >
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </Space>
      </template> -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '视频预览',
                icon: IconEnum.NEXT,
                type: 'primary',
                ghost: true,
                onClick: handlePreview.bind(null, record),
              },
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              // {
              //   label: '修改',
              //   icon: IconEnum.EDIT,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleEdit.bind(null, record),
              // },
              // {
              //   label: '删除',
              //   icon: IconEnum.DELETE,
              //   type: 'primary',
              //   danger: true,
              //   ghost: true,
              //   popConfirm: {
              //     placement: 'left',
              //     title: `是否确认删除?`,
              //     confirm: handleDelete.bind(null, record),
              //   },
              // },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <RecordedBroadcastModal @register="registerModal" @reload="reload" />
    <RecordedBroadcastInfoModal @register="registerInfoModal" />
    <PreviewModal @register="registerPreviewModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  // import { Space } from 'ant-design-vue';
  // recordedBroadcastExport,
  import { recordedBroadcastList, recordedBroadcastRemove } from '@/api/security/recordedBroadcast';
  import RecordedBroadcastModal from './Modal.vue';
  import { useModal } from '@/components/Modal';
  // import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';
  import RecordedBroadcastInfoModal from './InfoModal.vue';
  import PreviewModal from './PreviewModal.vue';

  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  });

  defineOptions({ name: 'RecordedBroadcast' });

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    showTableSetting: false,
    // title: '录播信息管理',
    showIndexColumn: false,
    api: (params) =>
      recordedBroadcastList({
        ...props.params,
        ...params,
      }),
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'recordedBroadcast',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 240,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  // function handleAdd() {
  //   openModal(true, { update: false });
  // }

  async function handleDelete(record: Recordable) {
    const { id } = record;
    await recordedBroadcastRemove([id]);
    await reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    const { id } = record;
    openInfoModal(true, id);
  }

  const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();

  function handlePreview(record: Recordable) {
    openPreviewModal(true, record);
  }
</script>

<style scoped></style>
