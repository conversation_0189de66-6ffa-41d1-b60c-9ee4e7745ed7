<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="事件管理详情"
    :canFullscreen="false"
    :footer="null"
    width="800px"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { Description, useDescription } from '@/components/Description';
  import { Skeleton } from 'ant-design-vue';
  import { eventManagementInfo } from '@/api/security/events';
  import { descSchema } from './info';

  defineOptions({ name: 'EventManagementInfoModal' });

  const showSkeleton = ref<boolean>(true);

  const [registerModal, { setModalProps }] = useModalInner(async (id: number) => {
    showSkeleton.value = true;
    setModalProps({ confirmLoading: true });
    try {
      const response = await eventManagementInfo(id);
      // 赋值给 Description 组件
      setDescProps({ data: response || {} });
      showSkeleton.value = false;
    } catch (error) {
      console.error('获取事件详情失败:', error);
      showSkeleton.value = false;
    } finally {
      setModalProps({ confirmLoading: false });
    }
  });

  const [registerDescription, { setDescProps }] = useDescription({
    column: 2,
    labelStyle: {
      width: '120px',
      minWidth: '120px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
