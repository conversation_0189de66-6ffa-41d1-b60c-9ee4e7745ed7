<template>
  <div class="event-stats-overview bg-white p-4 mx-4 mt-4 rounded-lg">
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- @click="handleStatClick(item.filterType)" -->
      <div
        v-for="(item, index) of statsModel"
        :key="index"
        class="stat-card bg-[rgba(0,52,119,0.04)] rounded-lg p-4 flex items-center space-x-2 cursor-pointer"
      >
        <img :src="item.icon" alt="" class="object-contain size-12" />

        <div class="flex-1 w-0">
          <span class="text-base text-gray-900">{{ item.label }}</span>
        </div>

        <div class="flex-none text-3xl font-bold text-primary-500">
          {{ item.value || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, watch } from 'vue';
  import { getToDayEventOverview } from '@/api/security/events';
  import stats1 from '@/assets/images/events/stats-1.png?url';
  import stats2 from '@/assets/images/events/stats-2.png?url';
  import stats3 from '@/assets/images/events/stats-3.png?url';
  import stats4 from '@/assets/images/events/stats-4.png?url';

  defineOptions({ name: 'EventStatsOverview' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const emit = defineEmits<{
    filterChange: [filterType: string | null];
  }>();

  const statsData = ref<{
    todayTotalCount: number;
    vehicleCount: number;
    materialCount: number;
    controlCount: number;
  }>({
    todayTotalCount: 0,
    vehicleCount: 0,
    materialCount: 0,
    controlCount: 0,
  });

  const statsModel = computed(() => {
    return [
      {
        label: '今日异常事件',
        value: statsData.value.todayTotalCount,
        icon: stats1,
        filterType: null, // 不筛选，显示全部
      },
      {
        label: '今日车辆检修异常事件',
        value: statsData.value.vehicleCount,
        icon: stats2,
        filterType: '2', // 车辆异常
      },
      {
        label: '今日物料仓库异常事件',
        value: statsData.value.materialCount,
        icon: stats3,
        filterType: '1', // 物料异常
      },
      {
        label: '今日作业管控异常事件',
        value: statsData.value.controlCount,
        icon: stats4,
        filterType: '3', // 作业管控异常
      },
    ];
  });

  // 获取统计数据
  async function fetchStatsData(params?: any) {
    try {
      const response = await getToDayEventOverview(params);
      statsData.value = response || {
        todayTotalCount: 0,
        vehicleCount: 0,
        materialCount: 0,
        controlCount: 0,
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      statsData.value = {
        todayTotalCount: 0,
        vehicleCount: 0,
        materialCount: 0,
        controlCount: 0,
      };
    }
  }

  // 处理统计卡片点击事件
  function handleStatClick(filterType: string | null) {
    emit('filterChange', filterType);
  }

  // 监听查询参数变化
  watch(
    () => props.searchParams,
    (newParams) => {
      fetchStatsData(newParams);
    },
    { deep: true, immediate: false },
  );

  onMounted(() => {
    fetchStatsData(props.searchParams);
  });

  // 暴露刷新方法供父组件调用
  defineExpose({
    refresh: (params?: any) => fetchStatsData(params),
  });
</script>

<style scoped>
  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  }


</style>
