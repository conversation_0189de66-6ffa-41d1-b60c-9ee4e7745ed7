import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';
// import {
//   eventTypeDict,
//   eventLevelDict,
//   eventStatusDict,
//   taskTypeDict,
//   eventTypeMap,
//   eventLevelMap,
//   eventStatusMap,
//   taskTypeMap,
//   eventLevelColorMap,
//   eventStatusColorMap,
//   eventTypeColorMap
// } from './dict';

export const { renderDict } = useRender();

/**
 * 表格列配置
 */
export const columns: BasicColumn[] = [
  {
    title: '任务类型',
    dataIndex: 'taskType',
    width: 120,
    customRender({ value }) {
      return renderDict(value, DictEnum.TASK_TYPE);
    },
  },
  {
    title: '异常事件信息',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '事件类型',
    dataIndex: 'eventType',
    width: 150,
    customRender({ value }) {
      return renderDict(value, DictEnum.EVENT_TYPE);
    },
  },
  {
    title: '事件级别',
    dataIndex: 'eventLevel',
    width: 100,
    customRender({ value }) {
      return renderDict(value, DictEnum.EVENT_LEVEL);
    },
  },
  {
    title: '责任人',
    dataIndex: 'responsiblePerson',
    width: 100,
  },
  {
    title: '事件状态',
    dataIndex: 'status',
    width: 100,
    customRender({ value }) {
      return renderDict(value, DictEnum.EVENT_STATUS);
    },
  },
  {
    title: '发生时间',
    dataIndex: 'occurrenceTime',
    width: 160,
  },
  {
    title: '事件编号',
    dataIndex: 'id',
    width: 100,
  },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createTime',
  //   width: 160,
  // },
];

/**
 * 搜索表单配置
 */
export const formSchemas: FormSchema[] = [
  {
    label: '任务类型',
    field: 'taskType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TASK_TYPE),
    },
  },
  {
    label: '异常事件信息',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '事件类型',
    field: 'eventType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.EVENT_TYPE),
    },
  },
  {
    label: '事件级别',
    field: 'eventLevel',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.EVENT_LEVEL),
    },
  },
  {
    label: '责任人',
    field: 'responsiblePerson',
    component: 'Input',
  },
  {
    label: '事件状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.EVENT_STATUS),
    },
  },
  {
    label: '事件编号',
    field: 'id',
    component: 'Input',
  },
  {
    label: 'AR眼镜编号',
    field: 'arGlassNum',
    component: 'Input',
  },
  {
    label: '发生时间',
    field: 'occurrenceTime',
    component: 'RangePicker',
  },
  // {
  //   label: '创建时间',
  //   field: 'createTime',
  //   component: 'RangePicker',
  // },
];

/**
 * 弹窗表单配置
 */
export const modalSchemas: FormSchema[] = [
  {
    label: '主键',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '任务类型',
    field: 'taskType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.TASK_TYPE),
    },
    required: true,
  },
  {
    label: '异常事件信息',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      rows: 3,
    },
  },
  {
    label: '事件类型',
    field: 'eventType',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.EVENT_TYPE),
    },
    required: true,
  },
  {
    label: '事件级别',
    field: 'eventLevel',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.EVENT_LEVEL),
    },
    required: true,
  },
  {
    label: '责任人',
    field: 'responsiblePerson',
    component: 'Input',
  },
  {
    label: '事件状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.EVENT_STATUS),
    },
    required: true,
  },
  {
    label: '发生时间',
    field: 'occurrenceTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
    required: true,
  },
  {
    label: '关联任务ID',
    field: 'taskId',
    component: 'Input',
  },
  {
    label: 'AR眼镜ID',
    field: 'arGlassId',
    component: 'Input',
  },
  {
    label: 'AR眼镜编号',
    field: 'arGlassNum',
    component: 'Input',
  },
  {
    label: '附件地址',
    field: 'annexUrl',
    component: 'Input',
  },
  {
    label: '附件类型',
    field: 'annexType',
    component: 'Input',
  },
];
