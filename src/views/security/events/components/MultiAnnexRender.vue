<template>
  <div v-if="!annexUrls.length" class="text-gray-400">-</div>
  <div v-else class="flex flex-wrap gap-2">
    <!-- 图片使用 PreviewGroup -->
    <Image.PreviewGroup v-if="imageUrls.length > 0">
      <div
        v-for="(url, index) in imageUrls"
        :key="`image-${index}`"
        class="annex-item"
      >
        <Image
          :width="60"
          :height="60"
          :src="url"
          :style="{
            objectFit: 'cover',
            borderRadius: '4px',
            border: '1px solid #d9d9d9'
          }"
          alt=''
          :fallback="defaultImageFallback"
        />
      </div>
    </Image.PreviewGroup>
    
    <!-- 其他文件类型 -->
    <div
      v-for="(url, index) in otherUrls"
      :key="`other-${index}`"
      class="annex-item annex-file"
      @click="openFile(url)"
    >
      <!-- 视频文件 -->
      <template v-if="checkVideo(url)">
        <div class="file-icon video-icon">
          <div class="play-button">▶</div>
        </div>
        <div class="file-label">视频</div>
      </template>
      
      <!-- 音频文件 -->
      <template v-else-if="checkAudio(url)">
        <div class="file-icon audio-icon">
          <div class="audio-symbol">♪</div>
        </div>
        <div class="file-label">音频</div>
      </template>
      
      <!-- 文档文件 -->
      <template v-else-if="checkDocument(url)">
        <div class="file-icon document-icon">
          <div class="document-symbol">📄</div>
        </div>
        <div class="file-label">文档</div>
      </template>
      
      <!-- 默认文件类型 -->
      <template v-else>
        <div class="file-icon default-icon">
          <div class="default-symbol">📎</div>
        </div>
        <div class="file-label">文件</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { Image } from 'ant-design-vue';

  interface Props {
    value?: string;
  }

  const props = withDefaults(defineProps<Props>(), {
    value: '',
  });

  // 默认图片占位符
  const defaultImageFallback = ''
  
  // 处理多个附件地址（逗号分隔）
  const annexUrls = computed(() => {
    if (!props.value) return [];
    return props.value.split(',').map((url: string) => url.trim()).filter((url: string) => url);
  });

  // 文件类型检测函数
  const checkImage = (url: string) => 
    ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp'].some((ext) => url.toLowerCase().includes(ext));
  
  const checkVideo = (url: string) => 
    ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'].some((ext) => url.toLowerCase().includes(ext));
  
  const checkAudio = (url: string) => 
    ['.mp3', '.wav', '.ogg', '.aac', '.flac'].some((ext) => url.toLowerCase().includes(ext));
  
  const checkDocument = (url: string) => 
    ['.pdf', '.doc', '.docx', '.txt', '.xls', '.xlsx', '.ppt', '.pptx'].some((ext) => url.toLowerCase().includes(ext));

  // 分离图片和其他文件
  const imageUrls = computed(() => annexUrls.value.filter(checkImage));
  const otherUrls = computed(() => annexUrls.value.filter((url: string) => !checkImage(url)));

  // 打开文件
  const openFile = (url: string) => {
    window.open(url, '_blank');
  };
</script>

<style scoped>
  .annex-item {
    @apply relative;

    width: 60px;
    height: 60px;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }

  .annex-file {
    @apply flex flex-col items-center justify-center cursor-pointer bg-gray-50;

    transition: all 0.2s ease;
  }

  .annex-file:hover {
    @apply bg-gray-100 shadow-sm;

    transform: translateY(-1px);
  }

  .file-icon {
    @apply flex items-center justify-center rounded;

    width: 24px;
    height: 24px;
    color: white;
    font-size: 12px;
  }

  .video-icon {
    @apply bg-blue-500;
  }

  .audio-icon {
    @apply bg-green-500;
  }

  .document-icon {
    @apply bg-orange-500;
  }

  .default-icon {
    @apply bg-gray-500;
  }

  .play-button,
  .audio-symbol,
  .document-symbol,
  .default-symbol {
    @apply flex items-center justify-center;
  }

  .file-label {
    @apply absolute bottom-0 left-0 right-0 text-center bg-white bg-opacity-80 text-gray-600;

    border-radius: 0 0 2px 2px;
    font-size: 10px;
    line-height: 14px;
  }
</style>
