/**
 * 事件类型字典
 */
export const eventTypeDict = [
  { label: '物料异常', value: '1' },
  { label: '车辆异常', value: '2' },
  { label: '作业管控异常', value: '3' },
];

/**
 * 事件类型映射
 */
export const eventTypeMap: Record<string, string> = {
  '1': '物料异常',
  '2': '车辆异常', 
  '3': '作业管控异常',
};

/**
 * 事件级别字典
 */
export const eventLevelDict = [
  { label: '紧急', value: '1' },
  { label: '重大', value: '2' },
  { label: '一般', value: '3' },
];

/**
 * 事件级别映射
 */
export const eventLevelMap: Record<string, string> = {
  '1': '紧急',
  '2': '重大',
  '3': '一般',
};

/**
 * 事件状态字典
 */
export const eventStatusDict = [
  { label: '未处理', value: '1' },
  { label: '已处理', value: '2' },
];

/**
 * 事件状态映射
 */
export const eventStatusMap: Record<string, string> = {
  '1': '未处理',
  '2': '已处理',
};

/**
 * 任务类型字典
 */
export const taskTypeDict = [
  { label: '料库任务', value: '1' },
  { label: '车辆检修任务', value: '2' },
  { label: '作业管控任务', value: '3' },
];

/**
 * 任务类型映射
 */
export const taskTypeMap: Record<string, string> = {
  '1': '料库任务',
  '2': '车辆检修任务',
  '3': '作业管控任务',
};

/**
 * 事件级别颜色映射
 */
export const eventLevelColorMap: Record<string, string> = {
  '1': 'red',      // 紧急
  '2': 'orange',   // 重大
  '3': 'blue',     // 一般
};

/**
 * 事件状态颜色映射
 */
export const eventStatusColorMap: Record<string, string> = {
  '1': 'orange',   // 未处理
  '2': 'green',    // 已处理
};

/**
 * 事件类型颜色映射
 */
export const eventTypeColorMap: Record<string, string> = {
  '1': 'purple',   // 物料异常
  '2': 'blue',     // 车辆异常
  '3': 'cyan',     // 作业管控异常
};
