<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
    :okButtonProps="{ loading: confirmLoading }"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { modalSchemas } from './data';
  import { eventManagementAdd, eventManagementUpdate } from '@/api/security/events';

  defineOptions({ name: 'EventManagementModal' });

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(true);
  const confirmLoading = ref(false);

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: modalSchemas,
    showActionButtonGroup: false,
    autoSubmitOnEnter: true,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.update;

    if (unref(isUpdate)) {
      const record = data.record;
      setFieldsValue({
        ...record,
      });
    }
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '新增事件管理' : '编辑事件管理'));

  async function handleSubmit() {
    try {
      const values = await validate();
      confirmLoading.value = true;
      
      // 处理时间格式
      if (values.occurrenceTime) {
        values.occurrenceTime = values.occurrenceTime.format('YYYY-MM-DD HH:mm:ss');
      }

      if (unref(isUpdate)) {
        await eventManagementUpdate(values);
      } else {
        await eventManagementAdd(values);
      }

      closeModal();
      emit('success', { isUpdate: unref(isUpdate), values: { ...values } });
    } finally {
      confirmLoading.value = false;
    }
  }
</script>
