<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="流程模板信息"
    :footer="null"
    @register="registerInnerModal"
  >
    <Description v-show="!showSkeleton" @register="registerDescription" />
    <Skeleton active v-if="showSkeleton" :paragraph="{ rows: 8 }" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { workflowTemplateInfo } from '@/api/security/railwayWorkflowTemplate';
  import { Description, useDescription } from '@/components/Description';
  import { descSchema } from './info';
  import { Skeleton } from 'ant-design-vue';
  import { ref } from 'vue';

  defineOptions({ name: 'WorkflowTemplateInfoModal' });

  const showSkeleton = ref<boolean>(true);
  const [registerInnerModal, { closeModal }] = useModalInner(
    async (workflowTemplateId: string | number) => {
      showSkeleton.value = true;
      if (!workflowTemplateId) {
        return closeModal();
      }
      const response = await workflowTemplateInfo(workflowTemplateId);
      // 赋值
      setDescProps({ data: response });
      showSkeleton.value = false;
    },
  );

  const [registerDescription, { setDescProps }] = useDescription({
    column: 1,
    labelStyle: {
      width: '150px',
      minWidth: '150px',
    },
    schema: descSchema,
  });
</script>

<style scoped></style>
