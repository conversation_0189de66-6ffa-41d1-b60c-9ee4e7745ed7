import { BasicColumn } from '@/components/Table';
import { FormSchema } from '@/components/Form';
import { useRender } from '@/hooks/component/useRender';
import { getDictOptions } from '@/utils/dict';
import { DictEnum } from '@/enums/dictEnum';

export const { renderDict } = useRender();

export const columns: BasicColumn[] = [
  { title: '模板名称', dataIndex: 'name' },
  { title: '模板描述', dataIndex: 'description' },
  {
    title: '状态',
    dataIndex: 'status',
    customRender({ value }) {
      return renderDict(value, DictEnum.WORKFLOW_STATUS);
    },
  },
  { title: '环节数量', dataIndex: 'workflowCount' },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建人', dataIndex: 'createBy' },
  { title: '创建时间', dataIndex: 'createTime' },
];

export const formSchemas: FormSchema[] = [
  {
    label: '模板名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '模板描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WORKFLOW_STATUS),
    },
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
  },
];

export const modalSchemas: FormSchema[] = [
  {
    label:'主键',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '模板名称',
    field: 'name',
    component: 'Input',
    required: true,
  },
  {
    label: '模板描述',
    field: 'description',
    component: 'InputTextArea',
    required: true,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: getDictOptions(DictEnum.WORKFLOW_STATUS),
    },
    required: true,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'workflowCount',
    label: '环节数量',
    component: 'InputNumber',
    show: false,
  },
  {
    field: 'createBy',
    label: '创建者',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Input',
    show: false,
  },
];
