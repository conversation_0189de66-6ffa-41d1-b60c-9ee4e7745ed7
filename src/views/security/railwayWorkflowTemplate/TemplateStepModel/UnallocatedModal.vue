<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="新增流程环节"
    :footer="null"
    @register="registerInnerModal"
  >
    <BasicTable
      @register="registerTable"
      :style="{ '--vben-basic-table-form-container-padding': 0 }"
    >
      <template #toolbar>
        <Space>
          <a-button type="primary" @click="handleAdd(null)" :disabled="!selected"
            >批量关联</a-button
          >
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '关联',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认关联?`,
                  confirm: handleAdd.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>

    <InfoModel @register="registerInfoModal" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import { formSchemas, columns } from '@/views/security/railwayWorkflowStep/data';
  import {
    unallocatedStepListByTemplateId,
    assignTemplateStep,
  } from '@/api/security/railwayWorkflowTemplateStep/index';
  import { IconEnum } from '@/enums/appEnum';

  import InfoModel from '@/views/security/railwayWorkflowStep/InfoModal.vue';

  defineOptions({ name: 'TemplateStepModel' });

  const currentWorkflowTemplateId = ref();

  const [registerTable, { reload, selected, getSelectRowKeys }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    showIndexColumn: false,
    immediate: false,
    api: (params) => {
      return unallocatedStepListByTemplateId({
        workflowId: currentWorkflowTemplateId.value,
        ...params,
      });
    },
    rowKey: 'id',
    useSearchForm: true,
    showTableSetting: false,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'workflowTemplate',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 160,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerInnerModal, { closeModal }] = useModalInner(
    async (workflowTemplateId: string | number) => {
      if (!workflowTemplateId) {
        return closeModal();
      }

      currentWorkflowTemplateId.value = workflowTemplateId;

      reload();
    },
  );

  async function handleAdd(record?: Recordable) {
    await assignTemplateStep({
      workflowId: currentWorkflowTemplateId.value,
      stepIds: record ? record.id : getSelectRowKeys().join(','),
    });
    reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    openInfoModal(true, record.id);
  }
</script>

<style scoped></style>
