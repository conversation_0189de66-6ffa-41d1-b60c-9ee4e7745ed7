<template>
  <BasicModal
    v-bind="$attrs"
    :width="600"
    title="已关联流程环节"
    :footer="null"
    @register="registerInnerModal"
  >
    <BasicTable
      @register="registerTable"
      :style="{ '--vben-basic-table-form-container-padding': 0 }"
    >
      <template #toolbar>
        <Space>
          <a-button type="primary" @click="handleAdd">新增</a-button>
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="handleUnAssign()"
            :disabled="!selected"
            >批量取消关联</a-button
          >
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              // {
              //   label: '查看',
              //   icon: IconEnum.PREVIEW,
              //   type: 'primary',
              //   ghost: true,
              //   onClick: handleInfo.bind(null, record),
              // },
              {
                label: '取消关联',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认取消关联?`,
                  confirm: handleUnAssign.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>

    <UnallocatedModal width="80%" @cancel="reload" @register="registerUnallocatedModal" />

    <InfoModel @register="registerInfoModal" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import { formSchemas, columns } from '@/views/security/railwayWorkflowStep/data';
  import {
    allocatedStepListByTemplateId,
    unassignTemplateStep,
  } from '@/api/security/railwayWorkflowTemplateStep/index';
  import { IconEnum } from '@/enums/appEnum';

  import UnallocatedModal from './UnallocatedModal.vue';

  import InfoModel from '@/views/security/railwayWorkflowStep/InfoModal.vue';

  defineOptions({ name: 'TemplateStepModel' });

  const showSkeleton = ref<boolean>(true);

  const currentWorkflowTemplateId = ref();

  const [registerTable, { reload, selected, getSelectRowKeys }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    showIndexColumn: false,
    immediate: false,
    api: (params) => {
      return allocatedStepListByTemplateId({
        workflowId: currentWorkflowTemplateId.value,
        ...params,
      });
    },
    rowKey: 'id',
    useSearchForm: true,
    showTableSetting: false,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'workflowTemplate',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
    },
    columns: columns,
    actionColumn: {
      width: 200,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerInnerModal, { closeModal }] = useModalInner(
    async (workflowTemplateId: string | number) => {
      showSkeleton.value = true;
      if (!workflowTemplateId) {
        return closeModal();
      }

      currentWorkflowTemplateId.value = workflowTemplateId;

      reload();
    },
  );

  const [registerUnallocatedModal, { openModal: openUnallocatedModal }] = useModal();

  function handleAdd() {
    openUnallocatedModal(true, currentWorkflowTemplateId.value);
  }

  async function handleUnAssign(record?: Recordable) {
    const templateStepIds = record ? [record.id] : getSelectRowKeys();
    await unassignTemplateStep(templateStepIds);
    reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    openInfoModal(true, record.id);
  }
</script>

<style scoped></style>
