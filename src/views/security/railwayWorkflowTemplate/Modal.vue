<template>
  <BasicModal
    v-bind="$attrs"
    :title="title"
    @register="registerInnerModal"
    @ok="handleSubmit"
    @cancel="resetForm"
  >
    <BasicForm @register="registerForm">
      <template #status="{ model, field }">
        <Select v-model:value="model[field]" placeholder="请选择" :allowClear="true">
          <SelectOption label="活跃" value="active" />
          <SelectOption label="非活跃" value="inactive" />
          <SelectOption label="已删除" value="deleted" />
        </Select>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { computed, ref, unref } from 'vue';
  import {
    workflowTemplateUpdate,
    workflowTemplateAdd,
    workflowTemplateInfo,
  } from '@/api/security/railwayWorkflowTemplate';
  import { modalSchemas } from './data';
  import { Select } from 'ant-design-vue';

  const SelectOption = Select.Option;

  defineOptions({ name: 'WorkflowTemplate' });

  const emit = defineEmits(['register', 'reload']);

  const isUpdate = ref<boolean>(false);

  const title = computed<string>(() => {
    return isUpdate.value ? '编辑流程模板' : '新增流程模板';
  });

  const retData = ref<any>({});

  const [registerInnerModal, { modalLoading, closeModal }] = useModalInner(
    async (data: { record?: Recordable; update: boolean }) => {
      modalLoading(true);
      const { record, update } = data;
      isUpdate.value = update;
      if (update && record) {
        const ret = await workflowTemplateInfo(record.id);
        retData.value = ret;

        await setFieldsValue(ret);
      }
      modalLoading(false);
    },
  );

  const [registerForm, { setFieldsValue, resetForm, validate, updateSchema }] = useForm({
    baseColProps: {
      span: 24,
    },
    labelWidth: 100,
    name: 'workflowTemplate_modal',
    showActionButtonGroup: false,
    schemas: modalSchemas,
  });

  async function handleSubmit() {
    try {
      modalLoading(true);
      const data = await validate();
      if (unref(isUpdate)) {
        await workflowTemplateUpdate(data);
      } else {
        await workflowTemplateAdd(data);
      }
      emit('reload');
      closeModal();
      await resetForm();
    } catch (e) {
      console.log(e);
    } finally {
      modalLoading(false);
    }
  }
</script>

<style scoped></style>
