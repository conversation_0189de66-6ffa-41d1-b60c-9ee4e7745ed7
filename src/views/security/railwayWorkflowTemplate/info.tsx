import { DescItem } from '@/components/Description';
import { useRender } from '@/hooks/component/useRender';
import { DictEnum } from '@/enums/dictEnum';

const { renderDict } = useRender();

export const descSchema: DescItem[] = [
  {
    label: '模板名称',
    field: 'name',
  },
  {
    label: '模板描述',
    field: 'description',
  },
  {
    label: '状态',
    field: 'status',
    render: (value) => {
      return renderDict(value, DictEnum.WORKFLOW_STATUS);
    },
  },
  {
    label: '环节数量',
    field: 'workflowCount',
  },
  {
    label: '备注',
    field: 'remark',
    render(value) {
      return value || '无';
    },
  },
  {
    label: '创建人',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];
