<template>
  <PageWrapper dense>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Space>
          <a-button
            class="<sm:hidden"
            @click="
              downloadExcel(workflowTemplateExport, '流程模板管理', getForm().getFieldsValue())
            "
            >导出</a-button
          >
          <a-button
            class="<sm:hidden"
            type="primary"
            danger
            @click="multipleRemove(workflowTemplateRemove)"
            :disabled="!selected"
            >删除</a-button
          >
          <a-button type="primary" @click="handleAdd">新增</a-button>
        </Space>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '流程环节',
                icon: IconEnum.NEXT,
                type: 'primary',
                ghost: true,
                onClick: handleStep.bind(null, record),
              },
              {
                label: '查看',
                icon: IconEnum.PREVIEW,
                type: 'primary',
                ghost: true,
                onClick: handleInfo.bind(null, record),
              },
              {
                label: '修改',
                icon: IconEnum.EDIT,
                type: 'primary',
                ghost: true,
                onClick: handleEdit.bind(null, record),
              },
              {
                label: '删除',
                icon: IconEnum.DELETE,
                type: 'primary',
                danger: true,
                ghost: true,
                popConfirm: {
                  placement: 'left',
                  title: `是否确认删除?`,
                  confirm: handleDelete.bind(null, record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <WorkflowTemplateModal @register="registerModal" @reload="reload" />
    <WorkflowTemplateInfoModal @register="registerInfoModal" />
    <TemplateStepModel width="80%" @register="registerTemplateStepModelModal" />
  </PageWrapper>
</template>

<script setup lang="ts">
  import { PageWrapper } from '@/components/Page';
  import { BasicTable, useTable, TableAction } from '@/components/Table';
  import { Space } from 'ant-design-vue';
  import {
    workflowTemplateList,
    workflowTemplateExport,
    workflowTemplateRemove,
  } from '@/api/security/railwayWorkflowTemplate';
  import WorkflowTemplateModal from './Modal.vue';
  import { useModal } from '@/components/Modal';
  import { downloadExcel } from '@/utils/file/download';
  import { formSchemas, columns } from './data';
  import { IconEnum } from '@/enums/appEnum';
  import WorkflowTemplateInfoModal from './InfoModal.vue';
  import TemplateStepModel from './TemplateStepModel/index.vue';

  defineOptions({ name: 'WorkflowTemplate' });

  const [registerTable, { reload, multipleRemove, selected, getForm }] = useTable({
    rowSelection: {
      type: 'checkbox',
    },
    title: '流程模板管理',
    showIndexColumn: false,
    api: workflowTemplateList,
    rowKey: 'id',
    useSearchForm: true,
    formConfig: {
      schemas: formSchemas,
      labelWidth: 100,
      name: 'workflowTemplate',
      baseColProps: {
        xs: 24,
        sm: 24,
        md: 24,
        lg: 6,
      },
      // 日期选择格式化
      fieldMapToTime: [
        ['createTime', ['params[beginTime]', 'params[endTime]'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ],
    },
    columns: columns,
    actionColumn: {
      width: 320,
      title: '操作',
      key: 'action',
      fixed: 'right',
    },
  });

  const [registerModal, { openModal }] = useModal();

  function handleEdit(record: Recordable) {
    openModal(true, { record, update: true });
  }

  function handleAdd() {
    openModal(true, { update: false });
  }

  async function handleDelete(record: Recordable) {
    const { id: workflowTemplateId } = record;
    await workflowTemplateRemove([workflowTemplateId]);
    await reload();
  }

  const [registerInfoModal, { openModal: openInfoModal }] = useModal();

  function handleInfo(record: Recordable) {
    const { id: workflowTemplateId } = record;
    openInfoModal(true, workflowTemplateId);
  }

  const [registerTemplateStepModelModal, { openModal: openTemplateStepModal }] = useModal();

  function handleStep(record: Recordable) {
    const { id: workflowTemplateId } = record;
    openTemplateStepModal(true, workflowTemplateId);
  }
</script>

<style scoped></style>
