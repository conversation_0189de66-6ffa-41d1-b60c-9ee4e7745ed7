import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const warehouse: AppRouteModule = {
  path: '/security/warehouse',
  name: 'SecurityWarehouse',
  component: LAYOUT,
  redirect: '/security/warehouse/task',
  meta: {
    orderNo: 50,
    icon: 'ant-design:container-outlined',
    title: '料库管理',
  },
  children: [
    {
      path: 'task',
      name: 'WarehouseTask',
      component: () => import('@/views/security/warehouse/index.vue'),
      meta: {
        title: '料库任务管理',
        icon: 'ant-design:file-text-outlined',
      },
    },
    {
      path: 'utensil',
      name: 'UtensilList',
      component: () => import('@/views/security/warehouse/utensil/index.vue'),
      meta: {
        title: '工具清单管理',
        icon: 'ant-design:tool-outlined',
      },
    },
  ],
};

export default warehouse;
