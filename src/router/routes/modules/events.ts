import type { AppRouteModule } from '@/router/types';
import { LAYOUT } from '@/router/constant';

const events: AppRouteModule = {
  path: '/security/events',
  name: 'SecurityEvents',
  component: LAYOUT,
  redirect: '/security/events/index',
  meta: {
    orderNo: 60,
    icon: 'ant-design:alert-outlined',
    title: '事件管理',
  },
  children: [
    {
      path: 'index',
      name: 'EventManagement',
      component: () => import('@/views/security/events/index.vue'),
      meta: {
        title: '事件管理',
        icon: 'ant-design:alert-outlined',
      },
    },
  ],
};

export default events;
