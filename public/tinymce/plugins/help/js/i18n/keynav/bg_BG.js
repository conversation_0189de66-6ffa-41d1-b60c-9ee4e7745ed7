tinymce.Resource.add('tinymce.html-i18n.help-keynav.bg_BG',
'<h1>Начало на навигацията с клавиатурата</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Фокусиране върху лентата с менюта</dt>\n' +
  '  <dd>Windows или Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Фокусиране върху лентата с инструменти</dt>\n' +
  '  <dd>Windows или Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Фокусиране върху долния колонтитул</dt>\n' +
  '  <dd>Windows или Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Фокусиране на известието</dt>\n' +
  '  <dd>Windows или Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Фокусиране върху контекстуалната лента с инструменти</dt>\n' +
  '  <dd>Windows, Linux или macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Навигацията ще започне с първия елемент на ПИ, който ще бъде маркиран или подчертан в случая на първия елемент в\n' +
  '  пътя до елемента в долния колонтитул.</p>\n' +
  '\n' +
  '<h1>Навигиране между раздели на ПИ</h1>\n' +
  '\n' +
  '<p>За да преминете от един раздел на ПИ към следващия, натиснете <strong>Tab</strong>.</p>\n' +
  '\n' +
  '<p>За да преминете от един раздел на ПИ към предишния, натиснете <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Редът за <strong>обхождане с табулация</strong> на тези раздели на ПИ е:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Лентата с менюта</li>\n' +
  '  <li>Всяка група на лентата с инструменти</li>\n' +
  '  <li>Страничната лента</li>\n' +
  '  <li>Пътят до елемента в долния колонтитул</li>\n' +
  '  <li>Бутонът за превключване на броя на думите в долния колонтитул</li>\n' +
  '  <li>Връзката за търговска марка в долния колонтитул</li>\n' +
  '  <li>Манипулаторът за преоразмеряване на редактора в долния колонтитул</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Ако някой раздел на ПИ липсва, той се пропуска.</p>\n' +
  '\n' +
  '<p>Ако долният колонтитул има фокус за навигация с клавиатурата и няма странична лента, натискането на <strong>Shift+Tab</strong>\n' +
  '  премества фокуса към първата група на лентата с инструменти, а не към последната.</p>\n' +
  '\n' +
  '<h1>Навигиране в разделите на ПИ</h1>\n' +
  '\n' +
  '<p>За да преминете от един елемент на ПИ към следващия, натиснете съответния клавиш със <strong>стрелка</strong>.</p>\n' +
  '\n' +
  '<p>С клавишите със стрелка <strong>наляво</strong> и <strong>надясно</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>се придвижвате между менютата в лентата с менюто;</li>\n' +
  '  <li>отваряте подменю в меню;</li>\n' +
  '  <li>се придвижвате между бутоните в група на лентата с инструменти;</li>\n' +
  '  <li>се придвижвате между елементи в пътя до елемент в долния колонтитул.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>С клавишите със стрелка <strong>надолу</strong> и <strong>нагоре</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>се придвижвате между елементите от менюто в дадено меню;</li>\n' +
  '  <li>се придвижвате между елементите в изскачащо меню на лентата с инструменти.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Клавишите със <strong>стрелки</strong> се придвижват в рамките на фокусирания раздел на ПИ.</p>\n' +
  '\n' +
  '<p>За да затворите отворено меню, подменю или изскачащо меню, натиснете клавиша <strong>Esc</strong>.</p>\n' +
  '\n' +
  '<p>Ако текущият фокус е върху „горната част“ на конкретен раздел на ПИ, натискането на клавиша <strong>Esc</strong> също излиза\n' +
  '  напълно от навигацията с клавиатурата.</p>\n' +
  '\n' +
  '<h1>Изпълнение на елемент от менюто или бутон от лентата с инструменти</h1>\n' +
  '\n' +
  '<p>Когато желаният елемент от менюто или бутон от лентата с инструменти е маркиран, натиснете <strong>Return</strong>, <strong>Enter</strong>\n' +
  '  или <strong>клавиша за интервал</strong>, за да изпълните елемента.</p>\n' +
  '\n' +
  '<h1>Навигиране в диалогови прозорци без раздели</h1>\n' +
  '\n' +
  '<p>В диалоговите прозорци без раздели първият интерактивен компонент се фокусира, когато се отвори диалоговият прозорец.</p>\n' +
  '\n' +
  '<p>Навигирайте между интерактивните компоненти на диалоговия прозорец, като натиснете <strong>Tab</strong> или <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>Навигиране в диалогови прозорци с раздели</h1>\n' +
  '\n' +
  '<p>В диалоговите прозорци с раздели първият бутон в менюто с раздели се фокусира, когато се отвори диалоговият прозорец.</p>\n' +
  '\n' +
  '<p>Навигирайте между интерактивните компоненти на този диалогов раздел, като натиснете <strong>Tab</strong> или\n' +
  '  <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Превключете към друг диалогов раздел, като фокусирате върху менюто с раздели и след това натиснете съответния клавиш със <strong>стрелка</strong>,\n' +
  '  за да преминете през наличните раздели.</p>\n');