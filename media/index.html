<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width">
<style>
html, body {
	margin: 0;
	padding: 0;
	height: 100%;
	font-family: 'Arial', sans-serif;
}
#video {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgb(30, 30, 30);
}
#message {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	text-align: center;
	justify-content: center;
	font-size: 16px;
	font-weight: bold;
	color: white;
	pointer-events: none;
	padding: 20px;
	box-sizing: border-box;
	text-shadow: 0 0 5px black;
}
</style>
<script defer src="./reader.js"></script>
</head>
<body>

<video id="video"></video>
<div id="message"></div>

<script>

const video = document.getElementById('video');
const message = document.getElementById('message');
let defaultControls = false;

const setMessage = (str) => {
  if (str !== '') {
    video.controls = false;
  } else {
    video.controls = defaultControls;
  }
  message.innerText = str;
};

const parseBoolString = (str, defaultVal) => {
  str = (str || '');

  if (['1', 'yes', 'true'].includes(str.toLowerCase())) {
    return true;
  }
  if (['0', 'no', 'false'].includes(str.toLowerCase())) {
    return false;
  }
  return defaultVal;
};

const loadAttributesFromQuery = () => {
  const params = new URLSearchParams(window.location.search);
  
  video.controls = parseBoolString(params.get('controls'), true);
  video.muted = parseBoolString(params.get('muted'), true);
  video.autoplay = parseBoolString(params.get('autoplay'), true);
  video.playsInline = parseBoolString(params.get('playsinline'), true);
  defaultControls = video.controls;

  return params
};

window.addEventListener('message', (event) => {
  loadAttributesFromQuery();
  
  const path = event.data.path || ''
  const headers = event.data.headers || {}

  const url = `http://*************:8889${path}/whep`

  new MediaMTXWebRTCReader({
    url,
    headers,
    onError: (err) => {
      setMessage(err);
    },
    onTrack: (evt) => {
      setMessage('');
      video.srcObject = evt.streams[0];
    },
  });
});
</script>

</body>
</html>
