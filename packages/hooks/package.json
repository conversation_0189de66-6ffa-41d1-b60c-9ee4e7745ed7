{"name": "@vben/hooks", "version": "1.0.0", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/hooks"}, "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"default": "./src/index.ts"}}, "main": "./src/index.ts", "module": "./src/index.ts", "files": ["dist"], "scripts": {"//build": "pnpm unbuild", "//stub": "pnpm unbuild --stub", "clean": "pnpm rimraf .turbo node_modules dist", "lint": "pnpm eslint ."}, "dependencies": {"@vueuse/core": "10.11.0", "lodash-es": "4.17.21", "vue": "3.4.37"}, "devDependencies": {"@vben/types": "workspace:*"}}